const express = require('express');
const { body, param } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const validateRequest = require('../middleware/validateRequest');
const { asyncHandler } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// List all locations
router.get(
  '/',
  requirePermission('INVENTORY', 'READ'),
  asyncHandler(async (req, res) => {
    const locations = await prisma.location.findMany({ orderBy: { name: 'asc' } });
    res.json({ success: true, data: locations });
  })
);

// Get a single location
router.get(
  '/:id',
  requirePermission('INVENTORY', 'READ'),
  [param('id').isString().withMessage('Location ID is required'), validateRequest],
  asyncHandler(async (req, res) => {
    const location = await prisma.location.findUnique({ where: { id: req.params.id } });
    if (!location) return res.status(404).json({ success: false, error: 'Location not found' });
    res.json({ success: true, data: location });
  })
);

// Create a new location
router.post(
  '/',
  requirePermission('INVENTORY', 'CREATE'),
  [
    body('name').isString().notEmpty().withMessage('Name is required'),
    body('address').optional().isString(),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { name, address } = req.body;
    const location = await prisma.location.create({ data: { name, address } });
    res.status(201).json({ success: true, data: location });
  })
);

// Update a location
router.put(
  '/:id',
  requirePermission('INVENTORY', 'UPDATE'),
  [
    param('id').isString().withMessage('Location ID is required'),
    body('name').optional().isString(),
    body('address').optional().isString(),
    body('isActive').optional().isBoolean(),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { name, address, isActive } = req.body;
    const location = await prisma.location.update({
      where: { id: req.params.id },
      data: { name, address, isActive },
    });
    res.json({ success: true, data: location });
  })
);

// Delete a location
router.delete(
  '/:id',
  requirePermission('INVENTORY', 'DELETE'),
  [param('id').isString().withMessage('Location ID is required'), validateRequest],
  asyncHandler(async (req, res) => {
    await prisma.location.delete({ where: { id: req.params.id } });
    res.json({ success: true });
  })
);

module.exports = router; 