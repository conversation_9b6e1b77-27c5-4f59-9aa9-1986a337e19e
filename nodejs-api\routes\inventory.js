const express = require("express");
const { body, param, query } = require("express-validator");
const { PrismaClient } = require("@prisma/client");
const validateRequest = require("../middleware/validateRequest");
const { asyncHandler } = require("../middleware/errorHandler");
const { requireRole, requirePermission } = require("../middleware/auth");

const router = express.Router();
const prisma = new PrismaClient();

// Get inventory for all variants
router.get(
  "/",
  requirePermission("INVENTORY", "READ"),
  [
    query("page")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Page must be a positive integer"),
    query("limit")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Limit must be between 1 and 100"),
    query("locationId")
      .optional()
      .isString()
      .withMessage("Location ID must be a string"),
    query("lowStock")
      .optional()
      .isBoolean()
      .withMessage("Low stock filter must be boolean"),
    query("search")
      .optional()
      .isString()
      .withMessage("Search term must be a string"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const locationId = req.query.locationId;
    const lowStock = req.query.lowStock === "true";
    const search = req.query.search;

    // Build where clause
    const where = {};
    if (locationId) {
      where.locationId = locationId;
    }
    if (lowStock) {
      where.quantity = {
        lte: prisma.inventory.findFirst({
          where: { id: prisma.raw("inventory.id") },
          select: { lowStockAlert: true },
        }),
      };
    }
    if (search) {
      where.variant = {
        OR: [
          { sku: { contains: search, mode: "insensitive" } },
          { name: { contains: search, mode: "insensitive" } },
          { product: { name: { contains: search, mode: "insensitive" } } },
        ],
      };
    }

    // Get inventory with pagination
    const [inventory, total] = await Promise.all([
      prisma.inventory.findMany({
        where,
        include: {
          variant: {
            include: {
              product: {
                select: {
                  name: true,
                  status: true,
                },
              },
            },
          },
          location: true,
          batches: true,
        },
        skip,
        take: limit,
        orderBy: { updatedAt: "desc" },
      }),
      prisma.inventory.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        inventory,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  })
);

// Get inventory by variant ID
router.get(
  "/variant/:variantId",
  requirePermission("INVENTORY", "READ"),
  [
    param("variantId").isString().withMessage("Variant ID is required"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { variantId } = req.params;

    const inventory = await prisma.inventory.findMany({
      where: { variantId },
      include: {
        location: true,
        variant: {
          include: {
            product: {
              select: {
                name: true,
                status: true,
              },
            },
          },
        },
        batches: true,
      },
    });

    if (!inventory) {
      return res.status(404).json({
        success: false,
        error: "Inventory not found for this variant",
      });
    }

    res.json({
      success: true,
      data: inventory,
    });
  })
);

// Update inventory
router.put(
  "/:id",
  requirePermission("INVENTORY", "UPDATE"),
  [
    param("id").isString().withMessage("Inventory ID is required"),
    body("quantity")
      .optional()
      .isInt({ min: 0 })
      .withMessage("Quantity must be a non-negative integer"),
    body("lowStockAlert")
      .optional()
      .isInt({ min: 0 })
      .withMessage("Low stock alert must be a non-negative integer"),
    body("reason")
      .isString()
      .withMessage("Reason for inventory update is required"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { quantity, lowStockAlert, reason } = req.body;

    // Start a transaction
    const result = await prisma.$transaction(async (prisma) => {
      // Get current inventory
      const currentInventory = await prisma.inventory.findUnique({
        where: { id },
        include: {
          variant: {
            include: {
              product: true,
            },
          },
          location: true,
        },
      });

      if (!currentInventory) {
        throw new Error("Inventory record not found");
      }

      // Calculate quantity change
      const quantityChange =
        quantity !== undefined ? quantity - currentInventory.quantity : 0;

      // Update inventory
      const updatedInventory = await prisma.inventory.update({
        where: { id },
        data: {
          quantity: quantity !== undefined ? quantity : undefined,
          lowStockAlert:
            lowStockAlert !== undefined ? lowStockAlert : undefined,
        },
        include: {
          variant: {
            include: {
              product: {
                select: {
                  name: true,
                  status: true,
                },
              },
            },
          },
          location: true,
        },
      });

      // Create inventory movement record
      if (quantityChange !== 0) {
        await prisma.inventoryMovement.create({
          data: {
            variantId: currentInventory.variantId,
            locationId: currentInventory.locationId,
            quantity: quantityChange,
            type: quantityChange > 0 ? "ADJUSTMENT_IN" : "ADJUSTMENT_OUT",
            reason,
            createdBy: req.user.id,
          },
        });
      }

      return updatedInventory;
    });

    res.json({
      success: true,
      data: result,
    });
  })
);

// Create inventory movement
router.post(
  "/movement",
  requirePermission("INVENTORY", "CREATE"),
  [
    body("variantId").isString().withMessage("Variant ID is required"),
    body("locationId").isString().withMessage("Location ID is required"),
    body("quantity")
      .isInt({ min: 1 })
      .withMessage("Quantity must be a positive integer"),
    body("type")
      .isIn([
        "PURCHASE",
        "SALE",
        "RETURN",
        "ADJUSTMENT_IN",
        "ADJUSTMENT_OUT",
        "TRANSFER_IN",
        "TRANSFER_OUT",
      ])
      .withMessage("Invalid movement type"),
    body("reason").isString().withMessage("Reason is required"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { variantId, locationId, quantity, type, reason } = req.body;

    const result = await prisma.$transaction(async (prisma) => {
      // Get or create inventory record
      let inventory = await prisma.inventory.findFirst({
        where: {
          variantId,
          locationId,
        },
      });

      if (!inventory) {
        inventory = await prisma.inventory.create({
          data: {
            variantId,
            locationId,
            quantity: 0,
            lowStockAlert: 10, // Default low stock alert
          },
        });
      }

      // Update inventory quantity
      const quantityChange = [
        "PURCHASE",
        "RETURN",
        "ADJUSTMENT_IN",
        "TRANSFER_IN",
      ].includes(type)
        ? quantity
        : -quantity;

      const updatedInventory = await prisma.inventory.update({
        where: { id: inventory.id },
        data: {
          quantity: {
            increment: quantityChange,
          },
        },
        include: {
          variant: {
            include: {
              product: {
                select: {
                  name: true,
                  status: true,
                },
              },
            },
          },
          location: true,
        },
      });

      // Create movement record
      const movement = await prisma.inventoryMovement.create({
        data: {
          variantId,
          locationId,
          quantity: quantityChange,
          type,
          reason,
          createdBy: req.user.id,
        },
      });

      return { inventory: updatedInventory, movement };
    });

    res.json({
      success: true,
      data: result,
    });
  })
);

// Get all low stock inventory items
router.get(
  '/low-stock',
  requirePermission('INVENTORY', 'READ'),
  asyncHandler(async (req, res) => {
    const lowStockItems = await prisma.inventory.findMany({
      where: {
        quantity: {
          lte: prisma.raw('"lowStockAlert"'),
        },
      },
      include: {
        variant: {
          include: {
            product: {
              select: { name: true, status: true },
            },
          },
        },
        location: true,
        batches: true,
      },
      orderBy: { updatedAt: 'desc' },
    });
    res.json({ success: true, data: lowStockItems });
  })
);

module.exports = router;
