"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { X, Plus } from "lucide-react";
import { toast } from "sonner";

interface Carrier {
  id: string;
  name: string;
  code: string;
  apiKey?: string;
  apiSecret?: string;
  isActive: boolean;
  services: string[];
  trackingUrl?: string;
  createdAt: string;
  updatedAt: string;
}

interface CarrierDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  carrier?: Carrier | null;
  onSubmit: (data: {
    name: string;
    code: string;
    apiKey?: string;
    apiSecret?: string;
    services: string[];
    trackingUrl?: string;
    isActive: boolean;
  }) => Promise<void>;
}

// Common carrier services
const COMMON_SERVICES = [
  "Ground",
  "Express",
  "Overnight",
  "Next Day Air",
  "2nd Day Air",
  "Priority Mail",
  "Express Mail",
  "Standard",
  "Expedited",
  "International",
  "Same Day",
  "Economy",
];

export function CarrierDialog({ open, onOpenChange, carrier, onSubmit }: CarrierDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    apiKey: "",
    apiSecret: "",
    trackingUrl: "",
    isActive: true,
  });
  const [services, setServices] = useState<string[]>([]);
  const [newService, setNewService] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when dialog opens/closes or carrier changes
  useEffect(() => {
    if (open) {
      if (carrier) {
        setFormData({
          name: carrier.name,
          code: carrier.code,
          apiKey: carrier.apiKey || "",
          apiSecret: carrier.apiSecret || "",
          trackingUrl: carrier.trackingUrl || "",
          isActive: carrier.isActive,
        });
        setServices(carrier.services || []);
      } else {
        setFormData({
          name: "",
          code: "",
          apiKey: "",
          apiSecret: "",
          trackingUrl: "",
          isActive: true,
        });
        setServices([]);
      }
      setNewService("");
    }
  }, [open, carrier]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addService = (serviceName: string) => {
    const trimmedService = serviceName.trim();
    if (trimmedService && !services.includes(trimmedService)) {
      setServices(prev => [...prev, trimmedService]);
      setNewService("");
    }
  };

  const removeService = (serviceName: string) => {
    setServices(prev => prev.filter(service => service !== serviceName));
  };

  const handleAddCustomService = () => {
    if (newService.trim()) {
      addService(newService);
    }
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error("Carrier name is required");
      return false;
    }
    if (!formData.code.trim()) {
      toast.error("Carrier code is required");
      return false;
    }
    if (formData.trackingUrl && !isValidUrl(formData.trackingUrl)) {
      toast.error("Please enter a valid tracking URL");
      return false;
    }
    return true;
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const submitData = {
        name: formData.name.trim(),
        code: formData.code.trim().toUpperCase(),
        apiKey: formData.apiKey.trim() || undefined,
        apiSecret: formData.apiSecret.trim() || undefined,
        services,
        trackingUrl: formData.trackingUrl.trim() || undefined,
        isActive: formData.isActive,
      };

      await onSubmit(submitData);
      onOpenChange(false);
    } catch (error) {
      console.error("Error submitting carrier:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto bg-black text-white">
        <DialogHeader>
          <DialogTitle>
            {carrier ? "Edit Carrier" : "Add New Carrier"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Carrier Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="e.g., FedEx, UPS, USPS"
                className="bg-gray-900 border-gray-700 text-white"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="code">Carrier Code</Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => handleInputChange("code", e.target.value.toUpperCase())}
                placeholder="e.g., FEDEX, UPS, USPS"
                className="bg-gray-900 border-gray-700 text-white"
              />
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="apiKey">API Key (optional)</Label>
              <Input
                id="apiKey"
                type="password"
                value={formData.apiKey}
                onChange={(e) => handleInputChange("apiKey", e.target.value)}
                placeholder="Enter API key"
                className="bg-gray-900 border-gray-700 text-white"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="apiSecret">API Secret (optional)</Label>
              <Input
                id="apiSecret"
                type="password"
                value={formData.apiSecret}
                onChange={(e) => handleInputChange("apiSecret", e.target.value)}
                placeholder="Enter API secret"
                className="bg-gray-900 border-gray-700 text-white"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="trackingUrl">Tracking URL Template (optional)</Label>
            <Input
              id="trackingUrl"
              value={formData.trackingUrl}
              onChange={(e) => handleInputChange("trackingUrl", e.target.value)}
              placeholder="e.g., https://fedex.com/track?id={trackingNumber}"
              className="bg-gray-900 border-gray-700 text-white"
            />
            <p className="text-xs text-gray-400">
              Use {"{trackingNumber}"} as placeholder for the tracking number
            </p>
          </div>

          <div className="space-y-4">
            <Label>Services</Label>
            
            {/* Selected Services */}
            {services.length > 0 && (
              <div className="space-y-2">
                <div className="text-sm text-gray-400">Available Services:</div>
                <div className="flex flex-wrap gap-2">
                  {services.map(service => (
                    <Badge
                      key={service}
                      variant="secondary"
                      className="bg-gray-800 text-white hover:bg-gray-700"
                    >
                      {service}
                      <button
                        type="button"
                        onClick={() => removeService(service)}
                        className="ml-2 hover:text-red-400"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Common Services */}
            <div className="space-y-2">
              <div className="text-sm text-gray-400">Add Common Services:</div>
              <div className="flex flex-wrap gap-2">
                {COMMON_SERVICES.filter(service => !services.includes(service)).map(service => (
                  <Button
                    key={service}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => addService(service)}
                    className="border-gray-700 text-white hover:bg-gray-800"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    {service}
                  </Button>
                ))}
              </div>
            </div>

            {/* Custom Service */}
            <div className="space-y-2">
              <div className="text-sm text-gray-400">Add Custom Service:</div>
              <div className="flex gap-2">
                <Input
                  value={newService}
                  onChange={(e) => setNewService(e.target.value)}
                  placeholder="Enter custom service name"
                  className="bg-gray-900 border-gray-700 text-white"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddCustomService();
                    }
                  }}
                />
                <Button
                  type="button"
                  onClick={handleAddCustomService}
                  disabled={!newService.trim()}
                  className="bg-white text-black hover:bg-gray-200"
                >
                  Add
                </Button>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => handleInputChange("isActive", checked)}
            />
            <Label htmlFor="isActive">Active</Label>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
              className="border-gray-700 text-white hover:bg-gray-800"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-white text-black hover:bg-gray-200"
            >
              {isSubmitting ? "Saving..." : carrier ? "Update Carrier" : "Add Carrier"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
