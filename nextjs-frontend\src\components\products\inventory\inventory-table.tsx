import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowUpDown, Settings } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Pagination } from '@/components/ui/pagination';
import { useState } from 'react';
import { ManageBatchesDialog } from './manage-batches-dialog';

interface InventoryTableProps {
  inventory: Array<{
    id: string;
    quantity: number;
    lowStockAlert: number;
    variant: {
      sku: string;
      name: string;
      product: {
        name: string;
        status: string;
      };
    };
    location: {
      name: string;
    };
  }>;
  loading: boolean;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onAdjustInventory: (inventory: any) => void;
  onCreateMovement: (inventory: any) => void;
  onRefresh?: () => void;
}

export function InventoryTable({
  inventory,
  loading,
  currentPage,
  totalPages,
  onPageChange,
  onAdjustInventory,
  onCreateMovement,
  onRefresh,
}: InventoryTableProps) {
  const [selectedInventory, setSelectedInventory] = useState<any | null>(null);
  const [showBatchesDialog, setShowBatchesDialog] = useState(false);

  const getStockStatus = (quantity: number, lowStockAlert: number) => {
    if (quantity === 0) return { label: 'Out of Stock', color: 'destructive' };
    if (quantity <= lowStockAlert) return { label: 'Low Stock', color: 'warning' };
    return { label: 'In Stock', color: 'success' };
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>SKU</TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Variant</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(5)].map((_, i) => (
                <TableRow key={i}>
                  <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-[60px]" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-[100px]" /></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>SKU</TableHead>
              <TableHead>Product</TableHead>
              <TableHead>Variant</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {inventory.map((item) => {
              const status = getStockStatus(item.quantity, item.lowStockAlert);
              return (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.variant.sku}</TableCell>
                  <TableCell>{item.variant.product.name}</TableCell>
                  <TableCell>{item.variant.name}</TableCell>
                  <TableCell>{item.location.name}</TableCell>
                  <TableCell>{item.quantity}</TableCell>
                  <TableCell>
                    <Badge variant={status.color as any}>{status.label}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onAdjustInventory(item)}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onCreateMovement(item)}
                      >
                        <ArrowUpDown className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedInventory(item);
                          setShowBatchesDialog(true);
                        }}
                      >
                        Batches
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
            {inventory.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No inventory records found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={onPageChange}
        />
      )}
      {/* Batches Dialog */}
      {showBatchesDialog && selectedInventory && (
        <ManageBatchesDialog
          open={showBatchesDialog}
          onClose={() => setShowBatchesDialog(false)}
          inventory={selectedInventory}
          onSuccess={() => {
            setShowBatchesDialog(false);
            if (onRefresh) onRefresh();
          }}
        />
      )}
    </div>
  );
} 