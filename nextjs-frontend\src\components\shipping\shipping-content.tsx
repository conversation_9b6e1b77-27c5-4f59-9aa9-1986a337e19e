"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Truck,
    Package,
    MapPin,
    Clock,
    DollarSign,
    Settings,
    Plus,
    Edit,
    Trash2,
    MoreHorizontal,
    Calendar,
    TrendingUp,
    AlertTriangle,
    Loader2
} from "lucide-react";
import { ShippingZoneDialog } from "./shipping-zone-dialog";
import { ShippingRateDialog } from "./shipping-rate-dialog";
import { CarrierDialog } from "./carrier-dialog";
import { api } from "@/lib/api";
import { toast } from "sonner";
import { usePermissions } from "@/contexts/auth-context";

// Types
interface ShippingZone {
    id: string;
    name: string;
    countries: string[];
    createdAt: string;
    updatedAt: string;
    rates?: ShippingRate[];
}

interface ShippingRate {
    id: string;
    zoneId: string;
    name: string;
    rate: number | string; // Backend returns Decimal as string
    minWeight?: number | string;
    maxWeight?: number | string;
    minPrice?: number | string;
    maxPrice?: number | string;
    freeShippingThreshold?: number | string;
    estimatedDays?: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    zone?: ShippingZone;
}

interface Carrier {
    id: string;
    name: string;
    code: string;
    apiKey?: string;
    apiSecret?: string;
    isActive: boolean;
    services: string[];
    trackingUrl?: string;
    createdAt: string;
    updatedAt: string;
}

interface Shipment {
    id: string;
    orderId: string;
    carrier: string;
    trackingNumber?: string;
    trackingUrl?: string;
    status: string;
    shippedAt?: string;
    deliveredAt?: string;
    createdAt: string;
    updatedAt: string;
    order?: {
        orderNumber: string;
        customer: {
            firstName: string;
            lastName: string;
            email: string;
        };
    };
}



// Helper function to format decimal values from backend
const formatDecimal = (value: number | string | undefined | null): string => {
    if (value === null || value === undefined) return '0.00';
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(num) ? '0.00' : num.toFixed(2);
};

export function ShippingContent() {
    // Permissions
    const { canCreate, canUpdate, canDelete } = usePermissions();

    // State for data
    const [shippingZones, setShippingZones] = useState<ShippingZone[]>([]);
    const [shippingRates, setShippingRates] = useState<ShippingRate[]>([]);
    const [carriers, setCarriers] = useState<Carrier[]>([]);
    const [shipments, setShipments] = useState<Shipment[]>([]);

    // State for loading
    const [isLoading, setIsLoading] = useState(true);

    // State for dialogs
    const [showZoneDialog, setShowZoneDialog] = useState(false);
    const [showRateDialog, setShowRateDialog] = useState(false);
    const [showCarrierDialog, setShowCarrierDialog] = useState(false);
    const [editingZone, setEditingZone] = useState<ShippingZone | null>(null);
    const [editingRate, setEditingRate] = useState<ShippingRate | null>(null);
    const [editingCarrier, setEditingCarrier] = useState<Carrier | null>(null);

    // Load initial data
    useEffect(() => {
        loadAllData();
    }, []);

    const loadAllData = async () => {
        setIsLoading(true);
        try {
            // Load data sequentially to avoid issues
            await loadShippingZones();
            await loadShippingRates();
            await loadCarriers();
            await loadShipments();
        } catch (error) {
            console.error("Error loading shipping data:", error);
            toast.error("Failed to load shipping data");
        } finally {
            setIsLoading(false);
        }
    };

    const loadShippingZones = async () => {
        try {
            const response = await api.get("/shipping/zones");
            if (response.success && response.data) {
                setShippingZones(response.data || []);
            } else {
                console.error("Failed to load shipping zones:", response.error);
                setShippingZones([]);
            }
        } catch (error) {
            console.error("Error loading shipping zones:", error);
            setShippingZones([]);
        }
    };

    const loadShippingRates = async () => {
        try {
            const response = await api.get("/shipping/rates");
            if (response.success && response.data) {
                setShippingRates(response.data || []);
            } else {
                console.error("Failed to load shipping rates:", response.error);
                setShippingRates([]);
            }
        } catch (error) {
            console.error("Error loading shipping rates:", error);
            setShippingRates([]);
        }
    };

    const loadCarriers = async () => {
        try {
            const response = await api.get("/shipping/carriers");
            if (response.success && response.data) {
                setCarriers(response.data || []);
            } else {
                console.error("Failed to load carriers:", response.error);
                setCarriers([]);
            }
        } catch (error) {
            console.error("Error loading carriers:", error);
            setCarriers([]);
        }
    };

    const loadShipments = async () => {
        try {
            const response = await api.get("/shipping?limit=10");
            if (response.success && response.data) {
                // Backend returns { success: true, data: { shipments, pagination } }
                setShipments(response.data.shipments || []);
            } else {
                console.error("Failed to load shipments:", response.error);
                setShipments([]);
            }
        } catch (error) {
            console.error("Error loading shipments:", error);
            setShipments([]);
        }
    };

    // Zone handlers
    const handleCreateZone = async (data: {
        name: string;
        countries: string[];
        rates?: {
            name: string;
            rate: number;
            estimatedDays?: string;
            freeShippingThreshold?: number;
        }[];
    }) => {
        try {
            const response = await api.post("/shipping/zones", data);
            if (response.success) {
                toast.success("Shipping zone created successfully");
                await loadShippingZones();
                await loadShippingRates(); // Refresh rates if any were created
            } else {
                toast.error(response.error || "Failed to create shipping zone");
            }
        } catch (error) {
            console.error("[Shipping] Error creating shipping zone:", error);
            toast.error("Failed to create shipping zone");
        }
    };

    const handleUpdateZone = async (data: { name: string; countries: string[] }) => {
        if (!editingZone) return;
        try {
            const response = await api.updateShippingZone(editingZone.id, data);
            if (response.success) {
                toast.success("Shipping zone updated successfully");
                await loadShippingZones();
                setEditingZone(null);
            } else {
                toast.error(response.error || "Failed to update shipping zone");
            }
        } catch (error) {
            console.error("Error updating shipping zone:", error);
            toast.error("Failed to update shipping zone");
        }
    };

    const handleDeleteZone = async (zoneId: string) => {
        try {
            const response = await api.deleteShippingZone(zoneId);
            if (response.success) {
                toast.success("Shipping zone deleted successfully");
                await loadShippingZones();
                await loadShippingRates(); // Refresh rates as they might be affected
            } else {
                toast.error(response.error || "Failed to delete shipping zone");
            }
        } catch (error) {
            console.error("Error deleting shipping zone:", error);
            toast.error("Failed to delete shipping zone");
        }
    };

    // Rate handlers
    const handleCreateRate = async (data: any) => {
        try {
            const response = await api.createShippingRate(data);
            if (response.success) {
                toast.success("Shipping rate created successfully");
                await loadShippingRates();
            } else {
                toast.error(response.error || "Failed to create shipping rate");
            }
        } catch (error) {
            console.error("Error creating shipping rate:", error);
            toast.error("Failed to create shipping rate");
        }
    };

    const handleUpdateRate = async (data: any) => {
        if (!editingRate) return;
        try {
            const response = await api.updateShippingRate(editingRate.id, data);
            if (response.success) {
                toast.success("Shipping rate updated successfully");
                await loadShippingRates();
                setEditingRate(null);
            } else {
                toast.error(response.error || "Failed to update shipping rate");
            }
        } catch (error) {
            console.error("Error updating shipping rate:", error);
            toast.error("Failed to update shipping rate");
        }
    };

    const handleDeleteRate = async (rateId: string) => {
        try {
            const response = await api.deleteShippingRate(rateId);
            if (response.success) {
                toast.success("Shipping rate deleted successfully");
                await loadShippingRates();
            } else {
                toast.error(response.error || "Failed to delete shipping rate");
            }
        } catch (error) {
            console.error("Error deleting shipping rate:", error);
            toast.error("Failed to delete shipping rate");
        }
    };

    // Carrier handlers
    const handleCreateCarrier = async (data: any) => {
        try {
            const response = await api.createCarrier(data);
            if (response.success) {
                toast.success("Carrier created successfully");
                await loadCarriers();
            } else {
                toast.error(response.error || "Failed to create carrier");
            }
        } catch (error) {
            console.error("Error creating carrier:", error);
            toast.error("Failed to create carrier");
        }
    };

    const handleUpdateCarrier = async (data: any) => {
        if (!editingCarrier) return;
        try {
            const response = await api.updateCarrier(editingCarrier.id, data);
            if (response.success) {
                toast.success("Carrier updated successfully");
                await loadCarriers();
                setEditingCarrier(null);
            } else {
                toast.error(response.error || "Failed to update carrier");
            }
        } catch (error) {
            console.error("Error updating carrier:", error);
            toast.error("Failed to update carrier");
        }
    };

    const handleDeleteCarrier = async (carrierId: string) => {
        try {
            const response = await api.deleteCarrier(carrierId);
            if (response.success) {
                toast.success("Carrier deleted successfully");
                await loadCarriers();
            } else {
                toast.error(response.error || "Failed to delete carrier");
            }
        } catch (error) {
            console.error("Error deleting carrier:", error);
            toast.error("Failed to delete carrier");
        }
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin" />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Shipping</h1>
                    <p className="text-muted-foreground">
                        Manage shipping zones, carriers, and track deliveries.
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline" onClick={loadAllData}>
                        <Loader2 className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        {isLoading ? 'Loading...' : 'Refresh Data'}
                    </Button>

                    <Button variant="outline">
                        <Calendar className="h-4 w-4 mr-2" />
                        Shipping Reports
                    </Button>
                    {canCreate('shipping') && (
                        <Button onClick={() => setShowZoneDialog(true)}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Shipping Zone
                        </Button>
                    )}
                </div>
            </div>



            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Active Shipments</CardTitle>
                        <Truck className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">145</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +12% from last week
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">On-Time Delivery</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">96.8%</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +0.3% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Avg. Shipping Cost</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">$16.45</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-red-500" />
                            +$1.20 from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Delivery Issues</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">3</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-red-500" />
                            2 more than last week
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Tabs */}
            <Tabs defaultValue="zones" className="space-y-6">
                <TabsList>
                    <TabsTrigger value="zones">Shipping Zones</TabsTrigger>
                    <TabsTrigger value="carriers">Carriers</TabsTrigger>
                    <TabsTrigger value="shipments">Recent Shipments</TabsTrigger>
                    <TabsTrigger value="settings">Settings</TabsTrigger>
                </TabsList>

                {/* Shipping Zones Tab */}
                <TabsContent value="zones" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Shipping Zones</CardTitle>
                            <CardDescription>
                                Configure shipping rates and delivery times for different regions
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Zone Name</TableHead>
                                        <TableHead>Countries</TableHead>
                                        <TableHead>Rates</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead></TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {isLoading ? (
                                        <TableRow>
                                            <TableCell colSpan={5} className="text-center">
                                                <Loader2 className="h-4 w-4 animate-spin mx-auto" />
                                                Loading shipping zones...
                                            </TableCell>
                                        </TableRow>
                                    ) : shippingZones.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={5} className="text-center text-gray-500">
                                                No shipping zones found. Create your first shipping zone to get started.
                                            </TableCell>
                                        </TableRow>
                                    ) : shippingZones.map((zone) => (
                                        <TableRow key={zone.id}>
                                            <TableCell className="font-medium">{zone.name}</TableCell>
                                            <TableCell>
                                                <div className="text-sm text-muted-foreground">
                                                    {zone.countries.slice(0, 3).join(", ")}
                                                    {zone.countries.length > 3 && ` +${zone.countries.length - 3} more`}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm text-muted-foreground">
                                                    {zone.rates?.length || 0} rate(s)
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm text-muted-foreground">
                                                    {new Date(zone.createdAt).toLocaleDateString()}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" className="h-8 w-8 p-0">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        {canUpdate('shipping') && (
                                                            <DropdownMenuItem onClick={() => {
                                                                setEditingZone(zone);
                                                                setShowZoneDialog(true);
                                                            }}>
                                                                <Edit className="h-4 w-4 mr-2" />
                                                                Edit Zone
                                                            </DropdownMenuItem>
                                                        )}
                                                        {canCreate('shipping') && (
                                                            <DropdownMenuItem onClick={() => {
                                                                setEditingRate(null);
                                                                setShowRateDialog(true);
                                                            }}>
                                                                <Settings className="h-4 w-4 mr-2" />
                                                                Add Rate
                                                            </DropdownMenuItem>
                                                        )}
                                                        {(canUpdate('shipping') || canDelete('shipping')) && (
                                                            <DropdownMenuSeparator />
                                                        )}
                                                        {canDelete('shipping') && (
                                                            <DropdownMenuItem
                                                                className="text-red-600"
                                                                onClick={() => handleDeleteZone(zone.id)}
                                                            >
                                                                <Trash2 className="h-4 w-4 mr-2" />
                                                                Delete Zone
                                                            </DropdownMenuItem>
                                                        )}
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>

                    {/* Shipping Rates Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Shipping Rates</CardTitle>
                            <CardDescription>
                                All configured shipping rates across zones
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Rate Name</TableHead>
                                        <TableHead>Zone</TableHead>
                                        <TableHead>Rate</TableHead>
                                        <TableHead>Estimated Days</TableHead>
                                        <TableHead>Free Shipping</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {isLoading ? (
                                        <TableRow>
                                            <TableCell colSpan={7} className="text-center">
                                                <Loader2 className="h-4 w-4 animate-spin mx-auto" />
                                                Loading shipping rates...
                                            </TableCell>
                                        </TableRow>
                                    ) : shippingRates.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={7} className="text-center text-gray-500">
                                                No shipping rates found. Create shipping zones with rates to get started.
                                            </TableCell>
                                        </TableRow>
                                    ) : shippingRates.map((rate) => (
                                        <TableRow key={rate.id}>
                                            <TableCell className="font-medium">{rate.name}</TableCell>
                                            <TableCell>
                                                <div className="text-sm text-muted-foreground">
                                                    {rate.zone?.name || 'Unknown Zone'}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="font-medium">
                                                    ${formatDecimal(rate.rate)}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm text-muted-foreground">
                                                    {rate.estimatedDays || 'Not specified'}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm text-muted-foreground">
                                                    {rate.freeShippingThreshold
                                                        ? `$${formatDecimal(rate.freeShippingThreshold)}+`
                                                        : 'Not available'
                                                    }
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant={rate.isActive ? "default" : "secondary"}>
                                                    {rate.isActive ? "Active" : "Inactive"}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" className="h-8 w-8 p-0">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        {canUpdate('shipping') && (
                                                            <DropdownMenuItem onClick={() => {
                                                                setEditingRate(rate);
                                                                setShowRateDialog(true);
                                                            }}>
                                                                <Edit className="h-4 w-4 mr-2" />
                                                                Edit Rate
                                                            </DropdownMenuItem>
                                                        )}
                                                        {canDelete('shipping') && (
                                                            <>
                                                                <DropdownMenuSeparator />
                                                                <DropdownMenuItem
                                                                    className="text-red-600"
                                                                    onClick={() => handleDeleteRate(rate.id)}
                                                                >
                                                                    <Trash2 className="h-4 w-4 mr-2" />
                                                                    Delete Rate
                                                                </DropdownMenuItem>
                                                            </>
                                                        )}
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Carriers Tab */}
                <TabsContent value="carriers" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        {/* Carriers List */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Shipping Carriers</CardTitle>
                                <CardDescription>
                                    Manage your shipping carrier integrations
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {isLoading ? (
                                    <div className="flex items-center justify-center p-8">
                                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                                        Loading carriers...
                                    </div>
                                ) : carriers.length === 0 ? (
                                    <div className="text-center p-8 text-gray-500">
                                        No carriers configured. Add your first carrier to get started.
                                    </div>
                                ) : carriers.map((carrier) => (
                                    <div key={carrier.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex items-center gap-4">
                                            <div className="h-12 w-12 bg-muted rounded-lg flex items-center justify-center">
                                                <Truck className="h-6 w-6" />
                                            </div>
                                            <div>
                                                <div className="flex items-center gap-2">
                                                    <span className="font-medium">{carrier.name}</span>
                                                    <Badge variant="outline">
                                                        {carrier.code}
                                                    </Badge>
                                                    <Badge
                                                        variant={carrier.isActive ? "default" : "secondary"}
                                                    >
                                                        {carrier.isActive ? "Active" : "Inactive"}
                                                    </Badge>
                                                </div>
                                                <div className="text-sm text-muted-foreground">
                                                    Services: {carrier.services.length > 0 ? carrier.services.join(", ") : "No services configured"}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => {
                                                    setEditingCarrier(carrier);
                                                    setShowCarrierDialog(true);
                                                }}
                                            >
                                                <Edit className="h-4 w-4 mr-1" />
                                                Edit
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleDeleteCarrier(carrier.id)}
                                                className="text-red-600 hover:text-red-700"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>

                        {/* Add New Carrier */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Add New Carrier</CardTitle>
                                <CardDescription>
                                    Connect a new shipping carrier
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {canCreate('shipping') ? (
                                    <Button
                                        className="w-full"
                                        onClick={() => setShowCarrierDialog(true)}
                                    >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add Carrier
                                    </Button>
                                ) : (
                                    <p className="text-center text-gray-500">
                                        You don't have permission to add carriers
                                    </p>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* Recent Shipments Tab */}
                <TabsContent value="shipments" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Shipments</CardTitle>
                            <CardDescription>
                                Track and manage your recent shipments
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Shipment ID</TableHead>
                                        <TableHead>Order</TableHead>
                                        <TableHead>Customer</TableHead>
                                        <TableHead>Carrier</TableHead>
                                        <TableHead>Tracking Number</TableHead>
                                        <TableHead>Destination</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Est. Delivery</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {isLoading ? (
                                        <TableRow>
                                            <TableCell colSpan={8} className="text-center">
                                                <Loader2 className="h-4 w-4 animate-spin mx-auto" />
                                                Loading shipments...
                                            </TableCell>
                                        </TableRow>
                                    ) : shipments.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={8} className="text-center text-gray-500">
                                                No shipments found. Create orders to generate shipments.
                                            </TableCell>
                                        </TableRow>
                                    ) : shipments.map((shipment) => (
                                        <TableRow key={shipment.id}>
                                            <TableCell className="font-medium">{shipment.id}</TableCell>
                                            <TableCell>{shipment.order?.orderNumber || shipment.orderId}</TableCell>
                                            <TableCell>
                                                {shipment.order?.customer
                                                    ? `${shipment.order.customer.firstName} ${shipment.order.customer.lastName}`
                                                    : 'Unknown Customer'
                                                }
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Truck className="h-4 w-4" />
                                                    {shipment.carrier}
                                                </div>
                                            </TableCell>
                                            <TableCell className="font-mono text-sm">
                                                {shipment.trackingNumber || 'Not assigned'}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-1">
                                                    <MapPin className="h-4 w-4" />
                                                    {shipment.order?.customer?.email || 'Unknown'}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant={
                                                    shipment.status === "DELIVERED" ? "default" :
                                                    shipment.status === "IN_TRANSIT" ? "secondary" :
                                                    shipment.status === "SHIPPED" ? "outline" : "destructive"
                                                }>
                                                    {shipment.status}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>{shipment.deliveredAt
                                                ? new Date(shipment.deliveredAt).toLocaleDateString()
                                                : 'Pending'
                                            }</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Settings Tab */}
                <TabsContent value="settings" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Default Settings</CardTitle>
                                <CardDescription>
                                    Configure default shipping preferences
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="default-carrier">Default Carrier</Label>
                                    <Select>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select default carrier" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="fedex">FedEx</SelectItem>
                                            <SelectItem value="ups">UPS</SelectItem>
                                            <SelectItem value="usps">USPS</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="package-weight">Default Package Weight (lbs)</Label>
                                    <Input id="package-weight" type="number" placeholder="1.0" />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="dimensions">Default Dimensions (L x W x H)</Label>
                                    <div className="grid grid-cols-3 gap-2">
                                        <Input placeholder="Length" />
                                        <Input placeholder="Width" />
                                        <Input placeholder="Height" />
                                    </div>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch id="insurance" />
                                    <Label htmlFor="insurance">Add insurance by default</Label>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch id="signature" />
                                    <Label htmlFor="signature">Require signature</Label>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Notifications</CardTitle>
                                <CardDescription>
                                    Configure shipping notifications
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="ship-notification">Shipment Created</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Notify customers when shipment is created
                                        </p>
                                    </div>
                                    <Switch id="ship-notification" />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="transit-notification">In Transit</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Send updates when package is in transit
                                        </p>
                                    </div>
                                    <Switch id="transit-notification" />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="delivery-notification">Delivered</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Confirm delivery to customers
                                        </p>
                                    </div>
                                    <Switch id="delivery-notification" />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="exception-notification">Exceptions</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Alert for delivery exceptions
                                        </p>
                                    </div>
                                    <Switch id="exception-notification" />
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>

            {/* Dialogs */}
            <ShippingZoneDialog
                open={showZoneDialog}
                onOpenChange={setShowZoneDialog}
                zone={editingZone}
                onSubmit={editingZone ? handleUpdateZone : handleCreateZone}
            />

            <ShippingRateDialog
                open={showRateDialog}
                onOpenChange={setShowRateDialog}
                rate={editingRate}
                zones={shippingZones}
                onSubmit={editingRate ? handleUpdateRate : handleCreateRate}
            />

            <CarrierDialog
                open={showCarrierDialog}
                onOpenChange={setShowCarrierDialog}
                carrier={editingCarrier}
                onSubmit={editingCarrier ? handleUpdateCarrier : handleCreateCarrier}
            />
        </div>
    );
}
