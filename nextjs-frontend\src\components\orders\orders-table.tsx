'use client';

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  Package,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Download,
  Calendar
} from 'lucide-react';
import { Order } from '@/lib/api';
import { format } from 'date-fns';
import { api } from '@/lib/api';
import { getToken } from '@/lib/api';
import { saveAs } from 'file-saver';

interface OrdersTableProps {
  orders: Order[];
  loading: boolean;
  onEdit: (order: Order) => void;
  onDelete: (orderId: string) => void;
  onUpdateStatus: (order: Order) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'PROCESSING':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'SHIPPED':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'DELIVERED':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'CANCELLED':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'REFUNDED':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    case 'ON_HOLD':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case 'PAID':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'FAILED':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'REFUNDED':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

function ordersToCSV(orders: Order[]): string {
  const headers = ['Order ID', 'Customer', 'Status', 'Total', 'Date'];
  const rows = orders.map(o => [
    o.id,
    o.customer,
    o.status,
    o.totalAmount?.toString() || '',
    o.createdAt ? new Date(o.createdAt).toLocaleString() : ''
  ]);
  return [headers, ...rows].map(r => r.map(x => `"${x}"`).join(',')).join('\n');
}

export function OrdersTable({
  orders,
  loading,
  onEdit,
  onDelete,
  onUpdateStatus,
  currentPage,
  totalPages,
  onPageChange,
}: OrdersTableProps) {
  const [selected, setSelected] = useState<string[]>([]);
  const [bulkAction, setBulkAction] = useState<null | 'delete' | 'export'>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const handleDelete = (orderId: string) => {
    setDeletingId(orderId);
    onDelete(orderId);
    setDeletingId(null);
  };

  const allSelected = orders.length > 0 && selected.length === orders.length;
  const toggleSelectAll = () => {
    if (allSelected) setSelected([]);
    else setSelected(orders.map(o => o.id));
  };
  const toggleSelect = (id: string) => {
    setSelected(sel => sel.includes(id) ? sel.filter(x => x !== id) : [...sel, id]);
  };

  const handleBulkDelete = async () => {
    if (selected.length === 0) return;
    if (!window.confirm(`Cancel ${selected.length} orders?`)) return;
    // Call backend bulk delete endpoint
    await fetch('/api/orders/bulk-delete', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ids: selected }),
    });
    setSelected([]);
    setBulkAction(null);
    // Optionally, refresh orders list here
  };

  const handleBulkExport = () => {
    const selectedOrders = orders.filter(o => selected.includes(o.id));
    const csv = ordersToCSV(selectedOrders);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, 'orders-export.csv');
    setBulkAction(null);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Payment</TableHead>
                <TableHead>Total</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(5)].map((_, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="h-4 w-20" />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-8 w-8" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="rounded-md border border-dashed p-8 text-center">
        <Package className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-2 text-sm font-medium text-muted-foreground">No orders found</h3>
        <p className="mt-1 text-sm text-muted-foreground">
          Get started by creating your first order.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead><input type="checkbox" checked={allSelected} onChange={toggleSelectAll} /></TableHead>
              <TableHead>Order</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Payment</TableHead>
              <TableHead>Items</TableHead>
              <TableHead>Total</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="w-[70px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orders.map((order) => (
              <TableRow key={order.id}>
                <TableCell><input type="checkbox" checked={selected.includes(order.id)} onChange={() => toggleSelect(order.id)} /></TableCell>
                <TableCell className="font-medium">
                  <div className="flex flex-col">
                    <span className="font-mono text-sm">#{order.orderNumber}</span>
                    {order.notes && Array.isArray(order.notes) && order.notes.length > 0 && (
                      <span className="text-xs text-muted-foreground">
                        {order.notes[0].note.length > 30 
                          ? `${order.notes[0].note.substring(0, 30)}...` 
                          : order.notes[0].note}
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="" />
                      <AvatarFallback>
                        {order.customer?.firstName?.[0]}{order.customer?.lastName?.[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">
                        {order.customer ? 
                          `${order.customer.firstName} ${order.customer.lastName}` : 
                          'Guest'
                        }
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {order.customer?.email}
                      </span>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={getStatusColor(order.status)}>
                    {order.status.replace('_', ' ')}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={getPaymentStatusColor(
                    order.payments && order.payments.length > 0 
                      ? order.payments[0].status 
                      : 'PENDING'
                  )}>
                    {order.payments && order.payments.length > 0 
                      ? order.payments[0].status 
                      : 'PENDING'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-1">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{order.items?.length || 0}</span>
                  </div>
                </TableCell>
                <TableCell className="font-medium">
                  {formatCurrency(order.totalAmount)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {format(new Date(order.createdAt), 'MMM dd, yyyy')}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => navigator.clipboard.writeText(order.id)}>
                        Copy order ID
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => onEdit(order)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit order
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onUpdateStatus(order)}>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Update status
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View details
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={async () => {
                          const token = getToken();
                          if (!token) {
                            alert('You must be logged in to view the invoice.');
                            return;
                          }
                          try {
                            const response = await fetch(`${api.baseURL}/orders/${order.id}/invoice`, {
                              headers: {
                                'Authorization': `Bearer ${token}`,
                              },
                            });
                            if (!response.ok) {
                              alert('Failed to fetch invoice');
                              return;
                            }
                            const html = await response.text();
                            const newWindow = window.open();
                            if (newWindow) {
                              newWindow.document.write(html);
                              newWindow.document.close();
                            }
                          } catch (err) {
                            alert('Error fetching invoice');
                          }
                        }}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Invoice
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                                                     <DropdownMenuItem 
                             onSelect={(e: Event) => e.preventDefault()}
                             className="text-red-600"
                           >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete order
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This action cannot be undone. This will permanently delete the order
                              and all associated data.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDelete(order.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-2">
          <div className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}