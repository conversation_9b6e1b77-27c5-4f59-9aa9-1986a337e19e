const express = require('express');
const bcrypt = require('bcryptjs');
const { body, param, query } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const validateRequest = require('../middleware/validateRequest');
const { asyncHandler } = require('../middleware/errorHandler');
const { requireRole, requirePermission } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Get all users with pagination and filters
router.get('/', requireRole(['ADMIN', 'MANAGER']), [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('role').optional().isIn(['ADMIN', 'MANAGER', 'STAFF']).withMessage('Invalid role'),
  query('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
  query('search').optional().isString().withMessage('Search must be a string'),
  validateRequest
], asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    role,
    isActive,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Build where clause
  const where = {};
  if (role) where.role = role;
  if (isActive !== undefined) where.isActive = isActive === 'true';
  if (search) {
    where.OR = [
      { firstName: { contains: search, mode: 'insensitive' } },
      { lastName: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } }
    ];
  }

  // Get users and total count
  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where,
      skip,
      take: parseInt(limit),
      orderBy: { [sortBy]: sortOrder },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            orders: true,
            permissions: true
          }
        }
      }
    }),
    prisma.user.count({ where })
  ]);

  res.json({
    success: true,
    data: {
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    }
  });
}));

// Get user by ID
router.get('/:id', requireRole(['ADMIN', 'MANAGER']), [
  param('id').isString().withMessage('User ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;

  const user = await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
      permissions: {
        select: {
          id: true,
          module: true,
          action: true,
          granted: true
        }
      },
      _count: {
        select: {
          orders: true,
          orderNotes: true
        }
      }
    }
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  res.json({
    success: true,
    data: user
  });
}));

// Create new user
router.post('/', requireRole(['ADMIN']), [
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('firstName').notEmpty().trim().withMessage('First name is required'),
  body('lastName').notEmpty().trim().withMessage('Last name is required'),
  body('role').isIn(['ADMIN', 'MANAGER', 'STAFF']).withMessage('Invalid role'),
  body('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { email, password, firstName, lastName, role, isActive = true } = req.body;

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email }
  });

  if (existingUser) {
    return res.status(409).json({
      success: false,
      error: 'User already exists with this email'
    });
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(password, 12);

  // Create user
  const user = await prisma.user.create({
    data: {
      email,
      password: hashedPassword,
      firstName,
      lastName,
      role,
      isActive
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true
    }
  });

  res.status(201).json({
    success: true,
    message: 'User created successfully',
    data: user
  });
}));

// Update user
router.put('/:id', requireRole(['ADMIN']), [
  param('id').isString().withMessage('User ID is required'),
  body('email').optional().isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('firstName').optional().notEmpty().trim().withMessage('First name cannot be empty'),
  body('lastName').optional().notEmpty().trim().withMessage('Last name cannot be empty'),
  body('role').optional().isIn(['ADMIN', 'MANAGER', 'STAFF']).withMessage('Invalid role'),
  body('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { email, firstName, lastName, role, isActive } = req.body;

  // Check if user exists
  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Check if email is already taken by another user
  if (email && email !== existingUser.email) {
    const emailExists = await prisma.user.findUnique({
      where: { email }
    });

    if (emailExists) {
      return res.status(409).json({
        success: false,
        error: 'Email is already taken'
      });
    }
  }

  // Prepare update data
  const updateData = {};
  if (email) updateData.email = email;
  if (firstName) updateData.firstName = firstName;
  if (lastName) updateData.lastName = lastName;
  if (role) updateData.role = role;
  if (isActive !== undefined) updateData.isActive = isActive;

  // Update user
  const user = await prisma.user.update({
    where: { id },
    data: updateData,
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      isActive: true,
      createdAt: true,
      updatedAt: true
    }
  });

  res.json({
    success: true,
    message: 'User updated successfully',
    data: user
  });
}));

// Delete user
router.delete('/:id', requireRole(['ADMIN']), [
  param('id').isString().withMessage('User ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if user exists
  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Cannot delete self
  if (id === req.user.id) {
    return res.status(400).json({
      success: false,
      error: 'Cannot delete yourself'
    });
  }

  // Soft delete by setting isActive to false
  await prisma.user.update({
    where: { id },
    data: { isActive: false }
  });

  res.json({
    success: true,
    message: 'User deleted successfully'
  });
}));

// Reset user password
router.post('/:id/reset-password', requireRole(['ADMIN']), [
  param('id').isString().withMessage('User ID is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { newPassword } = req.body;

  // Check if user exists
  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Hash new password
  const hashedPassword = await bcrypt.hash(newPassword, 12);

  // Update password
  await prisma.user.update({
    where: { id },
    data: { password: hashedPassword }
  });

  res.json({
    success: true,
    message: 'Password reset successfully'
  });
}));

// Manage user permissions
router.put('/:id/permissions', requireRole(['ADMIN']), [
  param('id').isString().withMessage('User ID is required'),
  body('permissions').isArray().withMessage('Permissions must be an array'),
  body('permissions.*.module').notEmpty().withMessage('Module is required'),
  body('permissions.*.action').isIn(['CREATE', 'READ', 'UPDATE', 'DELETE']).withMessage('Invalid action'),
  body('permissions.*.granted').isBoolean().withMessage('Granted must be boolean'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { permissions } = req.body;

  // Check if user exists
  const existingUser = await prisma.user.findUnique({
    where: { id }
  });

  if (!existingUser) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Delete existing permissions
  await prisma.userPermission.deleteMany({
    where: { userId: id }
  });

  // Create new permissions
  const permissionData = permissions.map(perm => ({
    userId: id,
    module: perm.module,
    action: perm.action,
    granted: perm.granted
  }));

  await prisma.userPermission.createMany({
    data: permissionData
  });

  // Get updated user with permissions
  const updatedUser = await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      isActive: true,
      permissions: {
        select: {
          id: true,
          module: true,
          action: true,
          granted: true
        }
      }
    }
  });

  res.json({
    success: true,
    message: 'User permissions updated successfully',
    data: updatedUser
  });
}));

module.exports = router;
