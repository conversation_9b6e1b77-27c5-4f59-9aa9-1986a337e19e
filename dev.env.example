# Development Environment Variables
# Copy this file to .env and update values as needed

# Database Configuration (matches dev-db.sh)
DATABASE_URL=postgresql://peptides_user:dev_password_2024@localhost:5432/peptides_db

# Redis Configuration (matches dev-db.sh)
REDIS_URL=redis://localhost:6379

# Application Configuration
NODE_ENV=development
PORT=3001

# JWT Configuration (for development only)
JWT_SECRET=dev_jwt_secret_key_change_in_production
JWT_EXPIRES_IN=7d

# Stripe Configuration (use test keys for development)
STRIPE_SECRET_KEY=sk_test_your_stripe_test_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_test_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_test_webhook_secret_here

# Email Configuration (optional for development)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
EMAIL_FROM=<EMAIL>

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp

# Rate Limiting (more lenient for development)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Security (development settings)
BCRYPT_SALT_ROUNDS=10
SESSION_SECRET=dev_session_secret_change_in_production

# Logging
LOG_LEVEL=debug
LOG_FORMAT=dev

# Development Settings
DEBUG=true 