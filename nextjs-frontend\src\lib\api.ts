import { toast } from "sonner";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

// Types for API responses
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface PaginatedData<T> {
  items?: T[];
  users?: T[];
  customers?: T[];
  products?: T[];
  orders?: T[];
  collections?: T[];
  data?: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface Promotion {
  id: string;
  code: string;
  name: string;
  description?: string;
  type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SHIPPING' | 'BOGO' | 'VOLUME_DISCOUNT';
  value: number;
  minOrderAmount?: number;
  maxDiscount?: number;
  usageLimit?: number;
  usageCount: number;
  isActive: boolean;
  startsAt?: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TaxRate {
  id: string;
  country: string;
  state?: string;
  rate: number;
  type: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data?: {
    items: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  error?: string;
}

// User types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: "ADMIN" | "MANAGER" | "STAFF";
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  permissions?: Permission[];
}

export interface Permission {
  id: string;
  module: string;
  action: string;
  granted: boolean;
}

// Customer types
export interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  email: string;
  mobile: string;
  customerType: "B2C" | "B2B" | "ENTERPRISE";
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  addresses?: Address[];
  customerTags?: CustomerTag[];
  _count?: {
    orders: number;
    addresses: number;
    reviews: number;
  };
}

export interface Address {
  id: string;
  customerId: string;
  type: "BILLING" | "SHIPPING";
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerTag {
  id: string;
  tag: string;
}

// Product types
export interface Product {
  id: string;
  name: string;
  description?: string;
  status: "DRAFT" | "ACTIVE" | "INACTIVE" | "ARCHIVED";
  createdAt: string;
  updatedAt: string;
  seoTitle?: string;
  seoDescription?: string;
  seoSlug?: string;
  variants?: ProductVariant[];
  images?: ProductImage[];
  categories?: ProductCategory[];
  tags?: ProductTag[];
  _count?: {
    variants: number;
    reviews: number;
  };
}

export interface ProductVariant {
  id: string;
  productId: string;
  name: string;
  description?: string;
  sku: string;
  barcode?: string;
  regularPrice: number;
  salePrice?: number;
  costPrice?: number;
  weight?: number;
  hsn?: string;
  isActive: boolean;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  inventory?: {
    id: string;
    locationId: string;
    quantity: number;
    reservedQty: number;
  }[];
  variantOptions?: VariantOption[];
  segmentPrices?: {
    id: string;
    customerType: "B2C" | "B2B" | "WHOLESALE";
    regularPrice: number;
    salePrice?: number;
  }[];
  seoTitle?: string;
  seoDescription?: string;
  seoSlug?: string;
}

export interface VariantOption {
  id: string;
  name: string;
  value: string;
}

export interface ProductImage {
  id: string;
  url: string;
  altText?: string;
  sortOrder: number;
}

export interface ProductCategory {
  id: string;
  name: string;
}

export interface ProductTag {
  id: string;
  tag: string;
}

export interface Inventory {
  id: string;
  quantity: number;
  reservedQty: number;
  locationId: string;
  location?: {
    id: string;
    name: string;
  };
}

// Order types
export interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  userId?: string;
  status:
    | "PENDING"
    | "PROCESSING"
    | "SHIPPED"
    | "DELIVERED"
    | "CANCELLED"
    | "REFUNDED"
    | "ON_HOLD";
  subtotal: number;
  discountAmount: number;
  shippingAmount: number;
  taxAmount: number;
  totalAmount: number;
  billingAddressId: string;
  shippingAddressId: string;
  createdAt: string;
  updatedAt: string;
  customer?: Customer;
  billingAddress?: Address;
  shippingAddress?: Address;
  items?: OrderItem[];
  payments?: Payment[];
  shipments?: Shipment[];
  notes?: OrderNote[];
  auditLogs?: AuditLog[];
  _count?: {
    items: number;
    notes: number;
  };
}

export interface OrderItem {
  id: string;
  orderId: string;
  variantId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  variant?: ProductVariant & {
    product?: Product;
  };
}

export interface OrderNote {
  id: string;
  orderId: string;
  userId: string;
  note: string;
  isInternal: boolean;
  createdAt: string;
  user?: User;
}

export interface Payment {
  id: string;
  orderId: string;
  paymentMethod: string;
  status: string;
  amount: number;
  paidAt?: string;
}

export interface Shipment {
  id: string;
  orderId: string;
  carrier: string;
  trackingNumber?: string;
  status: string;
  shippedAt?: string;
}

export interface AuditLog {
  id: string;
  orderId?: string;
  userId: string;
  action: string;
  details: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  user?: User;
}

// Token management
export const TOKEN_KEY = "auth_token";

export const getToken = (): string | null => {
  if (typeof window === "undefined") return null;
  return localStorage.getItem(TOKEN_KEY);
};

export const setToken = (token: string): void => {
  if (typeof window === "undefined") return;
  localStorage.setItem(TOKEN_KEY, token);
};

export const removeToken = (): void => {
  if (typeof window === "undefined") return;
  localStorage.removeItem(TOKEN_KEY);
};

// Collection types
export interface Collection {
  id: string;
  name: string;
  description?: string;
  slug: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  products?: ProductCollection[];
  _count?: {
    products: number;
  };
}

export interface ProductCollection {
  id: string;
  collectionId: string;
  productId: string;
  sortOrder: number;
  product?: Product;
}

// API client class
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    const token = getToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
      console.log('[API] Sending Authorization header:', headers.Authorization);
    } else {
      console.log('[API] No token found for Authorization header');
    }

    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const config: RequestInit = {
        headers: this.getHeaders(),
        ...options,
      };

      const response = await fetch(url, config);
      const data = await response.json();

      // Check for 401 Unauthorized first
      if (response.status === 401) {
        removeToken();
        // Don't redirect here - let the ProtectedRoute handle it
        return {
          success: false,
          error: "Authentication required",
        };
      }

      if (!response.ok) {
        throw new Error(
          data.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return data;
    } catch (error) {
      console.error("API request failed:", error);

      if (error instanceof Error) {
        // Show error toast
        toast.error(error.message);

        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: false,
        error: "An unexpected error occurred",
      };
    }
  }

  // Add generic HTTP methods
  async get<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "GET" });
  }
  async post<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      headers: { ...this.getHeaders(), "Content-Type": "application/json" },
      body: body ? JSON.stringify(body) : undefined,
    });
  }
  async put<T = any>(endpoint: string, body?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      headers: { ...this.getHeaders(), "Content-Type": "application/json" },
      body: body ? JSON.stringify(body) : undefined,
    });
  }
  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE", headers: this.getHeaders() });
  }

  // Authentication endpoints
  async login(
    email: string,
    password: string
  ): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request("/auth/login", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    });
  }

  async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role?: string;
  }): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request("/auth/register", {
      method: "POST",
      body: JSON.stringify(userData),
    });
  }

  async getProfile(): Promise<ApiResponse<User>> {
    return this.request("/auth/profile");
  }

  async updateProfile(userData: {
    firstName?: string;
    lastName?: string;
    email?: string;
  }): Promise<ApiResponse<User>> {
    return this.request("/auth/profile", {
      method: "PUT",
      body: JSON.stringify(userData),
    });
  }

  async changePassword(
    currentPassword: string,
    newPassword: string
  ): Promise<ApiResponse> {
    return this.request("/auth/change-password", {
      method: "PUT",
      body: JSON.stringify({ currentPassword, newPassword }),
    });
  }

  async logout(): Promise<ApiResponse> {
    return this.request("/auth/logout", {
      method: "POST",
    });
  }

  // User management endpoints
  async getUsers(params?: {
    page?: number;
    limit?: number;
    role?: string;
    isActive?: boolean;
    search?: string;
  }): Promise<PaginatedResponse<User>> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    return this.request(`/users?${searchParams.toString()}`) as Promise<
      PaginatedResponse<User>
    >;
  }

  async getUser(id: string): Promise<ApiResponse<User>> {
    return this.request(`/users/${id}`);
  }

  async createUser(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role: string;
    isActive?: boolean;
  }): Promise<ApiResponse<User>> {
    return this.request("/users", {
      method: "POST",
      body: JSON.stringify(userData),
    });
  }

  async updateUser(
    id: string,
    userData: {
      email?: string;
      firstName?: string;
      lastName?: string;
      role?: string;
      isActive?: boolean;
    }
  ): Promise<ApiResponse<User>> {
    return this.request(`/users/${id}`, {
      method: "PUT",
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(id: string): Promise<ApiResponse> {
    return this.request(`/users/${id}`, {
      method: "DELETE",
    });
  }

  async resetUserPassword(
    id: string,
    newPassword: string
  ): Promise<ApiResponse> {
    return this.request(`/users/${id}/reset-password`, {
      method: "POST",
      body: JSON.stringify({ newPassword }),
    });
  }

  async updateUserPermissions(
    id: string,
    permissions: Permission[]
  ): Promise<ApiResponse<User>> {
    return this.request(`/users/${id}/permissions`, {
      method: "PUT",
      body: JSON.stringify({ permissions }),
    });
  }

  // Customer management endpoints
  async getCustomers(params?: {
    page?: number;
    limit?: number;
    customerType?: string;
    isActive?: boolean;
    search?: string;
  }): Promise<
    ApiResponse<{
      customers: Customer[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }>
  > {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await this.request<{
      customers: Customer[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }>(`/customers?${searchParams.toString()}`);

    if (response.success && response.data) {
      return {
        success: true,
        data: {
          customers: response.data.customers,
          pagination: response.data.pagination,
        },
      };
    }

    return response as ApiResponse<{
      customers: Customer[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }>;
  }

  async getCustomer(id: string): Promise<ApiResponse<Customer>> {
    return this.request(`/customers/${id}`);
  }

  async createCustomer(customerData: {
    firstName: string;
    lastName: string;
    middleName?: string;
    email: string;
    mobile: string;
    customerType?: string;
    isActive?: boolean;
    tags?: string[];
    addresses?: any[];
  }): Promise<ApiResponse<Customer>> {
    return this.request("/customers", {
      method: "POST",
      body: JSON.stringify(customerData),
    });
  }

  async updateCustomer(
    id: string,
    customerData: {
      firstName?: string;
      lastName?: string;
      middleName?: string;
      email?: string;
      mobile?: string;
      customerType?: string;
      isActive?: boolean;
      tags?: string[];
    }
  ): Promise<ApiResponse<Customer>> {
    return this.request(`/customers/${id}`, {
      method: "PUT",
      body: JSON.stringify(customerData),
    });
  }

  async deleteCustomer(id: string): Promise<ApiResponse> {
    return this.request(`/customers/${id}`, {
      method: "DELETE",
    });
  }

  // Product management endpoints
  async getProducts(params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
    category?: string;
    include?: {
      variants?: {
        include?: {
          inventory?: boolean;
        };
      };
    };
  }): Promise<ApiResponse<PaginatedData<Product>>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.status) queryParams.append("status", params.status);
    if (params?.search) queryParams.append("search", params.search);
    if (params?.category) queryParams.append("category", params.category);
    if (params?.include?.variants?.include?.inventory) {
      queryParams.append("include", "variants.inventory");
    }

    const response = await this.request<{
      products: Product[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }>(`/products?${queryParams}`);

    if (response.success && response.data) {
      return {
        success: true,
        data: {
          products: response.data.products,
          pagination: response.data.pagination,
        },
      };
    }

    return response as ApiResponse<PaginatedData<Product>>;
  }

  async getProduct(id: string): Promise<ApiResponse<Product>> {
    return this.request(`/products/${id}`);
  }

  async createProduct(productData: {
    name: string;
    description?: string;
    status?: string;
    categories?: string[];
    tags?: string[];
    images?: any[];
    variants: any[];
    seoTitle?: string;
    seoDescription?: string;
    seoSlug?: string;
  }): Promise<ApiResponse<Product>> {
    return this.request("/products", {
      method: "POST",
      body: JSON.stringify(productData),
    });
  }

  async updateProduct(
    id: string,
    productData: {
      name?: string;
      description?: string;
      status?: string;
      categories?: string[];
      tags?: string[];
      images?: any[];
      seoTitle?: string;
      seoDescription?: string;
      seoSlug?: string;
    }
  ): Promise<ApiResponse<Product>> {
    return this.request(`/products/${id}`, {
      method: "PUT",
      body: JSON.stringify(productData),
    });
  }

  async deleteProduct(id: string): Promise<ApiResponse> {
    return this.request(`/products/${id}`, {
      method: "DELETE",
    });
  }

  // Order management endpoints
  async getOrders(params?: {
    page?: number;
    limit?: number;
    status?: string;
    customerId?: string;
    search?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<ApiResponse<PaginatedData<Order>>> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await this.request<{
      orders: Order[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }>(`/orders?${searchParams.toString()}`);

    if (response.success && response.data) {
      return {
        success: true,
        data: {
          orders: response.data.orders,
          pagination: response.data.pagination,
        },
      };
    }

    return response as ApiResponse<PaginatedData<Order>>;
  }

  async getOrder(id: string): Promise<ApiResponse<Order>> {
    return this.request(`/orders/${id}`);
  }

  async createOrder(orderData: {
    customerId: string;
    billingAddressId: string;
    shippingAddressId: string;
    items: {
      variantId: string;
      quantity: number;
      unitPrice: string;
    }[];
    discountAmount?: string;
    shippingAmount?: string;
    taxAmount?: string;
    couponCode?: string;
  }): Promise<ApiResponse<Order>> {
    return this.request("/orders", {
      method: "POST",
      body: JSON.stringify(orderData),
    });
  }

  async updateOrder(
    id: string,
    orderData: {
      status?: string;
      billingAddressId?: string;
      shippingAddressId?: string;
      discountAmount?: string;
      shippingAmount?: string;
      taxAmount?: string;
    }
  ): Promise<ApiResponse<Order>> {
    return this.request(`/orders/${id}`, {
      method: "PUT",
      body: JSON.stringify(orderData),
    });
  }

  async updateOrderStatus(
    id: string,
    status: string,
    note?: string
  ): Promise<ApiResponse> {
    return this.request(`/orders/${id}/status`, {
      method: "PATCH",
      body: JSON.stringify({ status, note }),
    });
  }

  async addOrderNote(
    id: string,
    note: string,
    isInternal: boolean = true
  ): Promise<ApiResponse<OrderNote>> {
    return this.request(`/orders/${id}/notes`, {
      method: "POST",
      body: JSON.stringify({ note, isInternal }),
    });
  }

  async getOrderNotes(id: string): Promise<ApiResponse<OrderNote[]>> {
    return this.request(`/orders/${id}/notes`);
  }

  async deleteOrder(id: string): Promise<ApiResponse> {
    return this.request(`/orders/${id}`, {
      method: "DELETE",
    });
  }

  // File upload
  async uploadFile(
    file: File
  ): Promise<ApiResponse<{ url: string; filename: string }>> {
    const formData = new FormData();
    formData.append("file", file);

    const token = getToken();
    const headers: HeadersInit = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(`${this.baseURL}/upload`, {
        method: "POST",
        headers,
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return data;
    } catch (error) {
      console.error("File upload failed:", error);
      if (error instanceof Error) {
        toast.error(error.message);
        return {
          success: false,
          error: error.message,
        };
      }
      return {
        success: false,
        error: "File upload failed",
      };
    }
  }

  async createAddress(
    customerId: string,
    addressData: any
  ): Promise<ApiResponse<Address>> {
    return this.request(`/customers/${customerId}/addresses`, {
      method: "POST",
      body: JSON.stringify(addressData),
    });
  }

  async updateAddress(
    customerId: string,
    addressId: string,
    addressData: any
  ): Promise<ApiResponse<Address>> {
    return this.request(`/customers/${customerId}/addresses/${addressId}`, {
      method: "PUT",
      body: JSON.stringify(addressData),
    });
  }

  async deleteAddress(
    customerId: string,
    addressId: string
  ): Promise<ApiResponse> {
    return this.request(`/customers/${customerId}/addresses/${addressId}`, {
      method: "DELETE",
    });
  }

  // Inventory Management
  async getLocations(): Promise<ApiResponse<Location[]>> {
    return this.request("/locations");
  }

  async getInventory(params?: {
    page?: number;
    limit?: number;
    search?: string;
    locationId?: string;
    lowStock?: boolean;
  }): Promise<
    ApiResponse<{
      inventory: Array<{
        id: string;
        quantity: number;
        lowStockAlert: number;
        variant: {
          id: string;
          sku: string;
          name: string;
          product: {
            name: string;
            status: string;
          };
        };
        location: {
          id: string;
          name: string;
        };
      }>;
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }>
  > {
    const searchParams = new URLSearchParams();
    if (params) {
      if (params.page) searchParams.append("page", params.page.toString());
      if (params.limit) searchParams.append("limit", params.limit.toString());
      if (params.search) searchParams.append("search", params.search);
      if (params.locationId)
        searchParams.append("locationId", params.locationId);
      if (params.lowStock)
        searchParams.append("lowStock", params.lowStock.toString());
    }
    const queryString = searchParams.toString();
    return this.request(`/inventory${queryString ? `?${queryString}` : ""}`);
  }

  async updateInventory(
    id: string,
    data: {
      quantity?: number;
      lowStockAlert?: number;
      reason: string;
    }
  ): Promise<ApiResponse<any>> {
    return this.request(`/inventory/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async createInventoryMovement(data: {
    variantId: string;
    locationId: string;
    quantity: number;
    type:
      | "PURCHASE"
      | "SALE"
      | "RETURN"
      | "ADJUSTMENT_IN"
      | "ADJUSTMENT_OUT"
      | "TRANSFER_IN"
      | "TRANSFER_OUT";
    reason: string;
  }): Promise<ApiResponse<any>> {
    return this.request("/inventory/movement", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // Category Management
  async getCategories(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<
    ApiResponse<{
      categories: Array<{
        id: string;
        name: string;
        product: {
          name: string;
          status: string;
        };
      }>;
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }>
  > {
    const searchParams = new URLSearchParams();
    if (params) {
      if (params.page) searchParams.append("page", params.page.toString());
      if (params.limit) searchParams.append("limit", params.limit.toString());
      if (params.search) searchParams.append("search", params.search);
    }
    const queryString = searchParams.toString();
    return this.request(`/categories${queryString ? `?${queryString}` : ""}`);
  }

  async createCategory(data: { name: string; productId: string }): Promise<
    ApiResponse<{
      id: string;
      name: string;
      product: {
        name: string;
        status: string;
      };
    }>
  > {
    return this.request("/categories", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async updateCategory(
    id: string,
    data: {
      name: string;
    }
  ): Promise<
    ApiResponse<{
      id: string;
      name: string;
      product: {
        name: string;
        status: string;
      };
    }>
  > {
    return this.request(`/categories/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async deleteCategory(id: string): Promise<ApiResponse<void>> {
    return this.request(`/categories/${id}`, {
      method: "DELETE",
    });
  }

  async getCollections(params?: {
    page?: number;
    limit?: number;
    search?: string;
    isActive?: boolean;
  }): Promise<
    ApiResponse<{
      collections: Collection[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }>
  > {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());
    if (params?.search) queryParams.append("search", params.search);
    if (typeof params?.isActive === "boolean")
      queryParams.append("isActive", params.isActive.toString());

    return this.request<{
      collections: Collection[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        pages: number;
      };
    }>(`/collections?${queryParams.toString()}`);
  }

  async getCollection(id: string): Promise<ApiResponse<Collection>> {
    return this.request<Collection>(`/collections/${id}`);
  }

  async createCollection(collectionData: {
    name: string;
    description?: string;
    isActive?: boolean;
    sortOrder?: number;
    productIds?: string[];
  }): Promise<ApiResponse<Collection>> {
    return this.request<Collection>("/collections", {
      method: "POST",
      body: JSON.stringify(collectionData),
    });
  }

  async updateCollection(
    id: string,
    collectionData: {
      name?: string;
      description?: string;
      isActive?: boolean;
      sortOrder?: number;
    }
  ): Promise<ApiResponse<Collection>> {
    return this.request<Collection>(`/collections/${id}`, {
      method: "PATCH",
      body: JSON.stringify(collectionData),
    });
  }

  async deleteCollection(id: string): Promise<ApiResponse> {
    return this.request(`/collections/${id}`, {
      method: "DELETE",
    });
  }

  async updateCollectionProducts(
    id: string,
    productIds: string[]
  ): Promise<ApiResponse> {
    return this.request(`/collections/${id}/products`, {
      method: "PUT",
      body: JSON.stringify({ productIds }),
    });
  }

  async reorderCollectionProducts(
    id: string,
    productOrders: Array<{ productId: string; sortOrder: number }>
  ): Promise<ApiResponse> {
    return this.request(`/collections/${id}/products/reorder`, {
      method: "PATCH",
      body: JSON.stringify({ productOrders }),
    });
  }

  async createProductVariant(
    productId: string,
    variantData: {
      sku: string;
      name: string;
      description?: string;
      regularPrice: number;
      salePrice?: number;
      weight?: number;
      hsn?: string;
      isActive?: boolean;
      options?: { name: string; value: string }[];
      segmentPrices?: {
        customerType: "B2C" | "B2B" | "WHOLESALE";
        regularPrice: number;
        salePrice?: number;
      }[];
    }
  ): Promise<ApiResponse<ProductVariant>> {
    return this.request<ProductVariant>(`/products/${productId}/variants`, {
      method: "POST",
      body: JSON.stringify(variantData),
    });
  }

  async updateProductVariant(
    productId: string,
    variantId: string,
    variantData: {
      sku?: string;
      name?: string;
      description?: string;
      regularPrice?: number;
      salePrice?: number;
      weight?: number;
      hsn?: string;
      isActive?: boolean;
      options?: { name: string; value: string }[];
      segmentPrices?: {
        customerType: "B2C" | "B2B" | "WHOLESALE";
        regularPrice: number;
        salePrice?: number;
      }[];
    }
  ): Promise<ApiResponse<ProductVariant>> {
    return this.request<ProductVariant>(
      `/products/${productId}/variants/${variantId}`,
      {
        method: "PUT",
        body: JSON.stringify(variantData),
      }
    );
  }

  async deleteProductVariant(
    productId: string,
    variantId: string
  ): Promise<ApiResponse<void>> {
    return this.request<void>(`/products/${productId}/variants/${variantId}`, {
      method: "DELETE",
    });
  }

  // Inventory Location endpoints
  async createLocation(data: { name: string; address?: string }) {
    return this.post('/locations', data);
  }
  async updateLocation(id: string, data: { name?: string; address?: string; isActive?: boolean }) {
    return this.put(`/locations/${id}`, data);
  }
  async deleteLocation(id: string) {
    return this.delete(`/locations/${id}`);
  }

  async initiateOrderRefund(orderId: string, amount: number, reason: string) {
    const token = getToken();
    const response = await fetch(`${this.baseURL}/orders/${orderId}/refund`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({ amount, reason }),
    });
    return response.json();
  }
  async updateRefundStatus(refundId: string, status: string) {
    const token = getToken();
    const response = await fetch(`${this.baseURL}/orders/refunds/${refundId}/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({ status }),
    });
    return response.json();
  }

  // Transaction management endpoints
  async getTransactions(params?: {
    orderId?: string;
    paymentStatus?: string;
    paymentGatewayName?: string;
  }): Promise<ApiResponse<{ transactions: any[] }>> {
    const queryParams = new URLSearchParams();
    if (params?.orderId) queryParams.append('orderId', params.orderId);
    if (params?.paymentStatus) queryParams.append('paymentStatus', params.paymentStatus);
    if (params?.paymentGatewayName) queryParams.append('paymentGatewayName', params.paymentGatewayName);
    return this.get(`/transactions${queryParams.toString() ? `?${queryParams}` : ''}`);
  }

  async getTransaction(id: string): Promise<ApiResponse<any>> {
    return this.get(`/transactions/${id}`);
  }

  async createTransaction(data: {
    orderId: string;
    amount: string | number;
    paymentStatus: string;
    paymentGatewayName: string;
    paymentGatewayTransactionId?: string;
    paymentGatewayResponse?: string;
  }): Promise<ApiResponse<any>> {
    return this.post('/transactions', data);
  }

  // Promotion/Coupon management endpoints
  async getPromotions(params?: {
    page?: number;
    limit?: number;
    isActive?: boolean;
  }): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    return this.request(`/promotions?${searchParams.toString()}`);
  }

  async getPromotion(id: string): Promise<ApiResponse<Promotion>> {
    return this.get(`/promotions/${id}`);
  }

  // Tax Rates
  async getTaxRates(params?: { country?: string; state?: string }): Promise<ApiResponse<TaxRate[]>> {
    const searchParams = new URLSearchParams();
    if (params) {
      if (params.country) searchParams.append('country', params.country);
      if (params.state) searchParams.append('state', params.state);
    }

    return this.request(`/tax-rates?${searchParams.toString()}`);
  }

  async getApplicableTaxRate(country: string, state?: string): Promise<ApiResponse<TaxRate | null>> {
    const searchParams = new URLSearchParams();
    searchParams.append('country', country);
    if (state) searchParams.append('state', state);

    return this.request(`/tax-rates/applicable?${searchParams.toString()}`);
  }

  async getApplicableShippingRate(country: string, subtotal: number, weight?: number): Promise<ApiResponse<any | null>> {
    const searchParams = new URLSearchParams();
    searchParams.append('country', country);
    searchParams.append('subtotal', subtotal.toString());
    if (weight) searchParams.append('weight', weight.toString());

    return this.request(`/shipping/applicable-rate?${searchParams.toString()}`);
  }

  // Shipping Management
  async getShipments(params?: { page?: number; limit?: number; orderId?: string; status?: string }): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();
    if (params) {
      if (params.page) searchParams.append('page', params.page.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.orderId) searchParams.append('orderId', params.orderId);
      if (params.status) searchParams.append('status', params.status);
    }

    return this.request(`/shipping?${searchParams.toString()}`);
  }

  async getShipment(id: string): Promise<ApiResponse<any>> {
    return this.get(`/shipping/${id}`);
  }

  async getOrderShipments(orderId: string): Promise<ApiResponse<any>> {
    return this.get(`/shipping/order/${orderId}`);
  }

  async createShipment(data: {
    orderId: string;
    carrier: string;
    trackingNumber?: string;
    trackingUrl?: string;
    status?: string;
  }): Promise<ApiResponse<any>> {
    return this.post('/shipping', data);
  }

  async updateShipmentTracking(id: string, data: {
    trackingNumber?: string;
    trackingUrl?: string;
    status?: string;
    carrier?: string;
  }): Promise<ApiResponse<any>> {
    return this.put(`/shipping/${id}/tracking`, data);
  }

  async deleteShipment(id: string): Promise<ApiResponse<any>> {
    return this.delete(`/shipping/${id}`);
  }

  // Shipping Zones Management
  async getShippingZones(): Promise<ApiResponse<any>> {
    return this.get('/shipping/zones');
  }

  async createShippingZone(data: {
    name: string;
    countries: string[];
    rates?: {
      name: string;
      rate: number;
      estimatedDays?: string;
      freeShippingThreshold?: number;
    }[];
  }): Promise<ApiResponse<any>> {
    return this.post('/shipping/zones', data);
  }

  async updateShippingZone(id: string, data: {
    name?: string;
    countries?: string[];
  }): Promise<ApiResponse<any>> {
    return this.put(`/shipping/zones/${id}`, data);
  }

  async deleteShippingZone(id: string): Promise<ApiResponse<any>> {
    return this.delete(`/shipping/zones/${id}`);
  }

  // Shipping Rates Management
  async getShippingRates(zoneId?: string): Promise<ApiResponse<any>> {
    const params = zoneId ? `?zoneId=${zoneId}` : '';
    return this.get(`/shipping/rates${params}`);
  }

  async createShippingRate(data: {
    zoneId: string;
    name: string;
    rate: number;
    minWeight?: number;
    maxWeight?: number;
    minPrice?: number;
    maxPrice?: number;
    freeShippingThreshold?: number;
    estimatedDays?: string;
    isActive?: boolean;
  }): Promise<ApiResponse<any>> {
    return this.post('/shipping/rates', data);
  }

  async updateShippingRate(id: string, data: {
    name?: string;
    rate?: number;
    minWeight?: number;
    maxWeight?: number;
    minPrice?: number;
    maxPrice?: number;
    freeShippingThreshold?: number;
    estimatedDays?: string;
    isActive?: boolean;
  }): Promise<ApiResponse<any>> {
    return this.put(`/shipping/rates/${id}`, data);
  }

  async deleteShippingRate(id: string): Promise<ApiResponse<any>> {
    return this.delete(`/shipping/rates/${id}`);
  }

  // Carriers Management
  async getCarriers(): Promise<ApiResponse<any>> {
    return this.get('/shipping/carriers');
  }

  async createCarrier(data: {
    name: string;
    code: string;
    apiKey?: string;
    apiSecret?: string;
    services?: string[];
    trackingUrl?: string;
    isActive?: boolean;
  }): Promise<ApiResponse<any>> {
    return this.post('/shipping/carriers', data);
  }

  async updateCarrier(id: string, data: {
    name?: string;
    code?: string;
    apiKey?: string;
    apiSecret?: string;
    services?: string[];
    trackingUrl?: string;
    isActive?: boolean;
  }): Promise<ApiResponse<any>> {
    return this.put(`/shipping/carriers/${id}`, data);
  }

  async deleteCarrier(id: string): Promise<ApiResponse<any>> {
    return this.delete(`/shipping/carriers/${id}`);
  }

  // Product Relations Management
  async addProductRelation(productId: string, relatedProductId: string, type: 'RELATED' | 'UPSELL' | 'CROSS_SELL'): Promise<ApiResponse<any>> {
    return this.post(`/products/${productId}/relations`, {
      relatedProductId,
      type
    });
  }

  async removeProductRelation(productId: string, relationId: string): Promise<ApiResponse<any>> {
    return this.delete(`/products/${productId}/relations/${relationId}`);
  }

  async getProductRelations(productId: string): Promise<ApiResponse<any>> {
    return this.get(`/products/${productId}/relations`);
  }

  // Product Reviews Management
  async approveReview(reviewId: string): Promise<ApiResponse<any>> {
    return this.put(`/reviews/${reviewId}/approve`, {});
  }

  async deleteReview(reviewId: string): Promise<ApiResponse<any>> {
    return this.delete(`/reviews/${reviewId}`);
  }

  async getProductReviews(productId: string): Promise<ApiResponse<any>> {
    return this.get(`/products/${productId}/reviews`);
  }

  // Inventory Batch Management
  async getInventoryBatches(inventoryId: string): Promise<ApiResponse<any>> {
    return this.get(`/inventory-batches?inventoryId=${inventoryId}`);
  }

  async createInventoryBatch(data: {
    inventoryId: string;
    batchNumber: string;
    quantity: number;
    expiryDate?: string;
  }): Promise<ApiResponse<any>> {
    return this.post('/inventory-batches', data);
  }

  async updateInventoryBatch(id: string, data: {
    batchNumber?: string;
    quantity?: number;
    expiryDate?: string;
  }): Promise<ApiResponse<any>> {
    return this.put(`/inventory-batches/${id}`, data);
  }

  async deleteInventoryBatch(id: string): Promise<ApiResponse<any>> {
    return this.delete(`/inventory-batches/${id}`);
  }

  async getExpiringBatches(days: number = 30): Promise<ApiResponse<any>> {
    return this.get(`/inventory-batches/expiring?days=${days}`);
  }

  async getExpiredBatches(): Promise<ApiResponse<any>> {
    return this.get('/inventory-batches/expired');
  }

  async validateCoupon(code: string): Promise<ApiResponse<Promotion>> {
    return this.get(`/promotions/code/${code}`);
  }

  async calculatePromotionDiscount(data: {
    promotionCode: string;
    orderItems: Array<{
      variantId: string;
      quantity: number;
      unitPrice: number;
      variant?: { productId?: string };
    }>;
    customerId?: string;
    subtotal: number;
    shippingAmount: number;
  }): Promise<ApiResponse<{ discount: number; appliedItems?: any[] }>> {
    return this.post('/promotions/calculate-discount', data);
  }

  async createPromotion(data: {
    code: string;
    name: string;
    description?: string;
    type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SHIPPING' | 'BOGO' | 'VOLUME_DISCOUNT';
    value: number;
    minOrderAmount?: number;
    maxDiscount?: number;
    usageLimit?: number;
    startsAt?: string;
    expiresAt?: string;
  }): Promise<ApiResponse<Promotion>> {
    return this.post('/promotions', data);
  }

  async updatePromotion(id: string, data: Partial<{
    name: string;
    description: string;
    type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SHIPPING' | 'BOGO' | 'VOLUME_DISCOUNT';
    value: number;
    minOrderAmount: number;
    maxDiscount: number;
    usageLimit: number;
    isActive: boolean;
    startsAt: string;
    expiresAt: string;
  }>): Promise<ApiResponse<Promotion>> {
    return this.put(`/promotions/${id}`, data);
  }

  async deletePromotion(id: string): Promise<ApiResponse<void>> {
    return this.delete(`/promotions/${id}`);
  }

  async useCoupon(code: string): Promise<ApiResponse<Promotion>> {
    return this.post(`/promotions/use/${code}`);
  }
}

// Create and export API client instance
export const api = new ApiClient(API_BASE_URL);

// Utility functions for common operations
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
};

export const formatDate = (date: string): string => {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(new Date(date));
};

export const getStatusColor = (status: string): string => {
  const statusColors: { [key: string]: string } = {
    PENDING: "bg-yellow-100 text-yellow-800",
    PROCESSING: "bg-blue-100 text-blue-800",
    SHIPPED: "bg-purple-100 text-purple-800",
    DELIVERED: "bg-green-100 text-green-800",
    CANCELLED: "bg-red-100 text-red-800",
    REFUNDED: "bg-gray-100 text-gray-800",
    ON_HOLD: "bg-orange-100 text-orange-800",
    ACTIVE: "bg-green-100 text-green-800",
    INACTIVE: "bg-gray-100 text-gray-800",
    DRAFT: "bg-gray-100 text-gray-800",
    ARCHIVED: "bg-red-100 text-red-800",
  };
  return statusColors[status] || "bg-gray-100 text-gray-800";
};
