const express = require('express');
const { body, param, query } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const validateRequest = require('../middleware/validateRequest');
const { asyncHandler } = require('../middleware/errorHandler');
const { authMiddleware, requirePermission } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// List/filter transactions
router.get(
  '/',
  authMiddleware,
  requirePermission('PAYMENTS', 'READ'),
  [
    query('orderId').optional().isString(),
    query('paymentStatus').optional().isString(),
    query('paymentGatewayName').optional().isString(),
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { orderId, paymentStatus, paymentGatewayName, page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const where = {};
    if (orderId) where.orderId = orderId;
    if (paymentStatus) where.paymentStatus = paymentStatus;
    if (paymentGatewayName) where.paymentGatewayName = paymentGatewayName;
    
    const [transactions, total] = await Promise.all([
      prisma.transaction.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { createdAt: 'desc' },
        include: {
          order: {
            include: {
              customer: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      }),
      prisma.transaction.count({ where })
    ]);
    
    res.json({ 
      success: true, 
      data: transactions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  })
);

// Get transaction details
router.get(
  '/:id',
  authMiddleware,
  requirePermission('PAYMENTS', 'READ'),
  [param('id').isString(), validateRequest],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const transaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        order: {
          include: {
            customer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            items: {
              include: {
                variant: {
                  include: {
                    product: {
                      select: {
                        id: true,
                        name: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });
    
    if (!transaction) {
      return res.status(404).json({ success: false, error: 'Transaction not found' });
    }
    
    res.json({ success: true, data: transaction });
  })
);

// Create a new transaction (manual/direct or future gateway)
router.post(
  '/',
  authMiddleware,
  requirePermission('PAYMENTS', 'CREATE'),
  [
    body('orderId').isString().withMessage('Order ID is required'),
    body('amount').isDecimal({ decimal_digits: '0,2' }).withMessage('Amount is required and must be a valid decimal'),
    body('paymentStatus').isIn(['PENDING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED']).withMessage('Invalid payment status'),
    body('paymentGatewayName').isIn(['DIRECT', 'STRIPE', 'PAYPAL', 'MANUAL', 'OTHER']).withMessage('Invalid payment gateway name'),
    body('paymentGatewayTransactionId').optional().isString(),
    body('paymentGatewayResponse').optional().isString(),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { 
      orderId, 
      amount, 
      paymentStatus, 
      paymentGatewayName, 
      paymentGatewayTransactionId, 
      paymentGatewayResponse 
    } = req.body;
    
    // Validate order exists
    const order = await prisma.order.findUnique({ 
      where: { id: orderId },
      include: {
        transactions: true,
      }
    });
    
    if (!order) {
      return res.status(404).json({ success: false, error: 'Order not found' });
    }
    
    // Calculate total paid amount
    const totalPaid = order.transactions
      .filter(t => t.paymentStatus === 'COMPLETED')
      .reduce((sum, t) => sum + parseFloat(t.amount.toString()), 0);
    
    const newAmount = parseFloat(amount);
    const orderTotal = parseFloat(order.totalAmount.toString());
    
    // Check for overpayment
    if (totalPaid + newAmount > orderTotal) {
      return res.status(400).json({ 
        success: false, 
        error: `Payment amount would exceed order total. Order total: $${orderTotal.toFixed(2)}, Already paid: $${totalPaid.toFixed(2)}, Remaining: $${(orderTotal - totalPaid).toFixed(2)}` 
      });
    }
    
    // Create transaction
    const transaction = await prisma.transaction.create({
      data: {
        orderId,
        amount: newAmount,
        paymentStatus,
        paymentGatewayName,
        paymentGatewayTransactionId,
        paymentGatewayResponse,
      },
      include: {
        order: {
          include: {
            customer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
      },
    });
    
    // Update order payment status if fully paid
    const newTotalPaid = totalPaid + (paymentStatus === 'COMPLETED' ? newAmount : 0);
    if (newTotalPaid >= orderTotal && paymentStatus === 'COMPLETED') {
      await prisma.order.update({
        where: { id: orderId },
        data: { paymentStatus: 'PAID' }
      });
    }
    
    res.status(201).json({ success: true, data: transaction });
  })
);

// Update transaction status
router.put(
  '/:id',
  authMiddleware,
  requirePermission('PAYMENTS', 'UPDATE'),
  [
    param('id').isString().withMessage('Transaction ID is required'),
    body('paymentStatus').optional().isIn(['PENDING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED']).withMessage('Invalid payment status'),
    body('paymentGatewayTransactionId').optional().isString(),
    body('paymentGatewayResponse').optional().isString(),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { paymentStatus, paymentGatewayTransactionId, paymentGatewayResponse } = req.body;
    
    const updateData = {};
    if (paymentStatus !== undefined) updateData.paymentStatus = paymentStatus;
    if (paymentGatewayTransactionId !== undefined) updateData.paymentGatewayTransactionId = paymentGatewayTransactionId;
    if (paymentGatewayResponse !== undefined) updateData.paymentGatewayResponse = paymentGatewayResponse;
    
    const transaction = await prisma.transaction.update({
      where: { id },
      data: updateData,
      include: {
        order: {
          include: {
            customer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            transactions: true,
          },
        },
      },
    });
    
    // Update order payment status if needed
    if (paymentStatus) {
      const order = transaction.order;
      const totalPaid = order.transactions
        .filter(t => t.paymentStatus === 'COMPLETED')
        .reduce((sum, t) => sum + parseFloat(t.amount.toString()), 0);
      
      const orderTotal = parseFloat(order.totalAmount.toString());
      
      if (totalPaid >= orderTotal) {
        await prisma.order.update({
          where: { id: order.id },
          data: { paymentStatus: 'PAID' }
        });
      } else if (totalPaid > 0) {
        await prisma.order.update({
          where: { id: order.id },
          data: { paymentStatus: 'PARTIAL' }
        });
      } else {
        await prisma.order.update({
          where: { id: order.id },
          data: { paymentStatus: 'PENDING' }
        });
      }
    }
    
    res.json({ success: true, data: transaction });
  })
);

// Delete transaction
router.delete(
  '/:id',
  authMiddleware,
  requirePermission('PAYMENTS', 'DELETE'),
  [param('id').isString().withMessage('Transaction ID is required'), validateRequest],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const transaction = await prisma.transaction.findUnique({
      where: { id },
      include: { order: true }
    });
    
    if (!transaction) {
      return res.status(404).json({ success: false, error: 'Transaction not found' });
    }
    
    await prisma.transaction.delete({
      where: { id }
    });
    
    // Recalculate order payment status
    const remainingTransactions = await prisma.transaction.findMany({
      where: { orderId: transaction.orderId }
    });
    
    const totalPaid = remainingTransactions
      .filter(t => t.paymentStatus === 'COMPLETED')
      .reduce((sum, t) => sum + parseFloat(t.amount.toString()), 0);
    
    const orderTotal = parseFloat(transaction.order.totalAmount.toString());
    
    let newPaymentStatus = 'PENDING';
    if (totalPaid >= orderTotal) {
      newPaymentStatus = 'PAID';
    } else if (totalPaid > 0) {
      newPaymentStatus = 'PARTIAL';
    }
    
    await prisma.order.update({
      where: { id: transaction.orderId },
      data: { paymentStatus: newPaymentStatus }
    });
    
    res.json({ success: true, message: 'Transaction deleted successfully' });
  })
);

module.exports = router;
