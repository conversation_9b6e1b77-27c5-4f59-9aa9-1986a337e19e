'use client';

import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card } from '@/components/ui/card';
import { Loader2, Plus, Trash2, Package, Target } from 'lucide-react';
import { api, Promotion } from '@/lib/api';
import { toast } from 'sonner';

interface EditCouponDialogProps {
  coupon: Promotion | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  description: string;
  type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SHIPPING' | 'BOGO' | 'VOLUME_DISCOUNT';
  value: string;
  minOrderAmount: string;
  maxDiscount: string;
  usageLimit: string;
  isActive: boolean;
  startsAt: string;
  expiresAt: string;

  // Customer segmentation
  customerTypes: string[];

  // BOGO specific
  bogoType: string;
  buyQuantity: string;
  getQuantity: string;
  getDiscount: string;

  // Product/Category rules
  productRules: Array<{
    productId?: string;
    variantId?: string;
    ruleType: 'BUY' | 'GET' | 'INCLUDE' | 'EXCLUDE';
    quantity?: string;
  }>;
  categoryRules: Array<{
    categoryId: string;
    ruleType: 'INCLUDE' | 'EXCLUDE';
  }>;

  // Volume tiers
  volumeTiers: Array<{
    minQuantity: string;
    maxQuantity?: string;
    discountType: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FIXED_PRICE';
    discountValue: string;
  }>;
}

interface FormErrors {
  name?: string;
  type?: string;
  value?: string;
  bogoType?: string;
  buyQuantity?: string;
  getQuantity?: string;
  getDiscount?: string;
  volumeTiers?: string;
  [key: string]: string | undefined; // For dynamic volume tier errors
}

export function EditCouponDialog({ coupon, open, onOpenChange, onSuccess }: EditCouponDialogProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    type: 'PERCENTAGE',
    value: '',
    minOrderAmount: '',
    maxDiscount: '',
    usageLimit: '',
    isActive: true,
    startsAt: '',
    expiresAt: '',
    customerTypes: [],
    bogoType: '',
    buyQuantity: '',
    getQuantity: '',
    getDiscount: '',
    productRules: [],
    categoryRules: [],
    volumeTiers: [],
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [products, setProducts] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);

  useEffect(() => {
    if (open) {
      fetchProducts();
      fetchCategories();
    }
  }, [open]);

  useEffect(() => {
    if (coupon) {
      setFormData({
        name: coupon.name,
        description: coupon.description || '',
        type: coupon.type,
        value: coupon.value.toString(),
        minOrderAmount: coupon.minOrderAmount?.toString() || '',
        maxDiscount: coupon.maxDiscount?.toString() || '',
        usageLimit: coupon.usageLimit?.toString() || '',
        isActive: coupon.isActive,
        startsAt: coupon.startsAt ? new Date(coupon.startsAt).toISOString().slice(0, 16) : '',
        expiresAt: coupon.expiresAt ? new Date(coupon.expiresAt).toISOString().slice(0, 16) : '',
        customerTypes: (coupon as any).customerTypes || [],
        bogoType: (coupon as any).bogoType || '',
        buyQuantity: (coupon as any).buyQuantity?.toString() || '',
        getQuantity: (coupon as any).getQuantity?.toString() || '',
        getDiscount: (coupon as any).getDiscount?.toString() || '',
        productRules: (coupon as any).productRules || [],
        categoryRules: (coupon as any).categoryRules || [],
        volumeTiers: (coupon as any).volumeTiers?.map((tier: any) => ({
          minQuantity: tier.minQuantity.toString(),
          maxQuantity: tier.maxQuantity?.toString() || '',
          discountType: tier.discountType,
          discountValue: tier.discountValue.toString(),
        })) || [],
      });
    }
  }, [coupon]);

  const fetchProducts = async () => {
    try {
      const response = await api.getProducts();
      if (response.success) {
        setProducts(response.data?.items || []);
      }
    } catch (error) {
      console.error('Failed to fetch products:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await api.getCategories();
      if (response.success) {
        setCategories(response.data?.categories || []);
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // Initialize volume tiers when Volume Discount is selected
      if (field === 'type' && value === 'VOLUME_DISCOUNT' && prev.volumeTiers.length === 0) {
        newData.volumeTiers = [{
          minQuantity: '',
          maxQuantity: '',
          discountType: 'PERCENTAGE',
          discountValue: ''
        }];
      }

      return newData;
    });

    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Coupon name is required';
    }

    if (!formData.type) {
      newErrors.type = 'Coupon type is required';
    }

    // Value validation - only for basic promotion types
    if (formData.type !== 'BOGO' && formData.type !== 'VOLUME_DISCOUNT') {
      if (!formData.value.trim()) {
        newErrors.value = 'Value is required';
      } else {
        const value = parseFloat(formData.value);
        if (isNaN(value) || value <= 0) {
          newErrors.value = 'Value must be a positive number';
        } else if (formData.type === 'PERCENTAGE' && value > 100) {
          newErrors.value = 'Percentage cannot exceed 100%';
        }
      }
    }

    // BOGO validation
    if (formData.type === 'BOGO') {
      if (!formData.bogoType) {
        newErrors.bogoType = 'BOGO type is required';
      }
      if (!formData.buyQuantity || parseInt(formData.buyQuantity) <= 0) {
        newErrors.buyQuantity = 'Buy quantity must be greater than 0';
      }
      if (!formData.getQuantity || parseInt(formData.getQuantity) <= 0) {
        newErrors.getQuantity = 'Get quantity must be greater than 0';
      }
      if ((formData.bogoType === 'BUY_X_GET_Y_PERCENT' || formData.bogoType === 'BUY_X_GET_Y_FIXED') &&
          (!formData.getDiscount || parseFloat(formData.getDiscount) <= 0)) {
        newErrors.getDiscount = 'Discount value is required';
      }
    }

    // Volume discount validation
    if (formData.type === 'VOLUME_DISCOUNT') {
      if (formData.volumeTiers.length === 0) {
        newErrors.volumeTiers = 'At least one volume tier is required';
      } else {
        formData.volumeTiers.forEach((tier, index) => {
          if (!tier.minQuantity || parseInt(tier.minQuantity) <= 0) {
            newErrors[`volumeTier_${index}_minQuantity`] = 'Min quantity is required';
          }
          if (!tier.discountValue || parseFloat(tier.discountValue) <= 0) {
            newErrors[`volumeTier_${index}_discountValue`] = 'Discount value is required';
          }
        });
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!coupon || !validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const updateData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        type: formData.type,
        value: formData.type === 'BOGO' || formData.type === 'VOLUME_DISCOUNT' ? 0 : parseFloat(formData.value),
        minOrderAmount: formData.minOrderAmount ? parseFloat(formData.minOrderAmount) : undefined,
        maxDiscount: formData.maxDiscount ? parseFloat(formData.maxDiscount) : undefined,
        usageLimit: formData.usageLimit ? parseInt(formData.usageLimit) : undefined,
        isActive: formData.isActive,
        startsAt: formData.startsAt || undefined,
        expiresAt: formData.expiresAt || undefined,

        // Customer segmentation
        customerTypes: formData.customerTypes,

        // BOGO specific fields
        ...(formData.type === 'BOGO' && {
          bogoType: formData.bogoType,
          buyQuantity: formData.buyQuantity ? parseInt(formData.buyQuantity) : undefined,
          getQuantity: formData.getQuantity ? parseInt(formData.getQuantity) : undefined,
          getDiscount: formData.getDiscount ? parseFloat(formData.getDiscount) : undefined,
        }),

        // Volume discount specific fields
        ...(formData.type === 'VOLUME_DISCOUNT' && {
          volumeTiers: formData.volumeTiers.map(tier => ({
            minQuantity: parseInt(tier.minQuantity),
            maxQuantity: tier.maxQuantity ? parseInt(tier.maxQuantity) : undefined,
            discountType: tier.discountType,
            discountValue: parseFloat(tier.discountValue),
          })),
        }),

        // Product and category rules
        productRules: formData.productRules,
        categoryRules: formData.categoryRules,
      };

      const response = await api.updatePromotion(coupon.id, updateData);

      if (response.success) {
        onSuccess();
      } else {
        toast.error(response.error || 'Failed to update coupon');
      }
    } catch (error) {
      console.error('Failed to update coupon:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!coupon) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Coupon</DialogTitle>
          <DialogDescription>
            Update the coupon details. Note: The coupon code cannot be changed.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 pb-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Coupon Code</Label>
              <Input
                value={coupon.code}
                disabled
                className="bg-muted"
              />
              <p className="text-xs text-muted-foreground">
                Coupon code cannot be changed
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Type *</Label>
              <Select value={formData.type} onValueChange={(value: any) => handleInputChange('type', value)}>
                <SelectTrigger className={errors.type ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PERCENTAGE">Percentage Discount</SelectItem>
                  <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                  <SelectItem value="FREE_SHIPPING">Free Shipping</SelectItem>
                  <SelectItem value="BOGO">Buy One Get One</SelectItem>
                  <SelectItem value="VOLUME_DISCOUNT">Volume Discount</SelectItem>
                </SelectContent>
              </Select>
              {errors.type && (
                <p className="text-sm text-red-600">{errors.type}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Coupon Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="20% Off Summer Sale"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Optional description for internal use"
              rows={2}
            />
          </div>

          {/* Value field - only show for basic promotion types */}
          {formData.type !== 'BOGO' && formData.type !== 'VOLUME_DISCOUNT' && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="value">
                  Value * {formData.type === 'PERCENTAGE' ? '(%)' : '($)'}
                </Label>
                <Input
                  id="value"
                  type="number"
                  step="0.01"
                  min="0"
                  max={formData.type === 'PERCENTAGE' ? '100' : undefined}
                  value={formData.value}
                  onChange={(e) => handleInputChange('value', e.target.value)}
                  placeholder={formData.type === 'PERCENTAGE' ? '20' : '10.00'}
                  className={errors.value ? 'border-red-500' : ''}
                />
                {errors.value && (
                  <p className="text-sm text-red-600">{errors.value}</p>
                )}
              </div>
            </div>
          )}

          {/* BOGO Specific Configuration */}
          {formData.type === 'BOGO' && (
            <Card className="p-4 space-y-4 border-gray-600 bg-black">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-white" />
                <h3 className="font-medium text-white">BOGO Configuration</h3>
              </div>

              <div className="space-y-2">
                <Label className="text-white">BOGO Type *</Label>
                <Select value={formData.bogoType} onValueChange={(value) => handleInputChange('bogoType', value)}>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue placeholder="Select BOGO type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BUY_X_GET_Y_FREE">Buy X Get Y Free</SelectItem>
                    <SelectItem value="BUY_X_GET_Y_PERCENT">Buy X Get Y at % Off</SelectItem>
                    <SelectItem value="BUY_X_GET_Y_FIXED">Buy X Get Y at Fixed Price</SelectItem>
                    <SelectItem value="CHEAPEST_FREE">Buy X Get Cheapest Free</SelectItem>
                    <SelectItem value="MOST_EXPENSIVE_FREE">Buy X Get Most Expensive Free</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-white">Buy Quantity *</Label>
                  <Input
                    type="number"
                    min="1"
                    value={formData.buyQuantity}
                    onChange={(e) => handleInputChange('buyQuantity', e.target.value)}
                    placeholder="2"
                    className="bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-white">Get Quantity *</Label>
                  <Input
                    type="number"
                    min="1"
                    value={formData.getQuantity}
                    onChange={(e) => handleInputChange('getQuantity', e.target.value)}
                    placeholder="1"
                    className="bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                  />
                </div>
              </div>

              {(formData.bogoType === 'BUY_X_GET_Y_PERCENT' || formData.bogoType === 'BUY_X_GET_Y_FIXED') && (
                <div className="space-y-2">
                  <Label className="text-white">
                    {formData.bogoType === 'BUY_X_GET_Y_PERCENT' ? 'Discount Percentage (%)' : 'Fixed Price ($)'}
                  </Label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    max={formData.bogoType === 'BUY_X_GET_Y_PERCENT' ? '100' : undefined}
                    value={formData.getDiscount}
                    onChange={(e) => handleInputChange('getDiscount', e.target.value)}
                    placeholder={formData.bogoType === 'BUY_X_GET_Y_PERCENT' ? '50' : '5.00'}
                    className="bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                  />
                </div>
              )}
            </Card>
          )}

          {/* Volume Discount Configuration */}
          {formData.type === 'VOLUME_DISCOUNT' && (
            <Card className="p-4 space-y-4 border-gray-600 bg-black">
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4 text-white" />
                <h3 className="font-medium text-white">Volume Discount Tiers</h3>
              </div>

              {formData.volumeTiers.length === 0 && (
                <div className="text-center py-4 text-gray-300">
                  <p className="text-sm">No volume tiers configured</p>
                  <p className="text-xs">Add tiers to create quantity-based discounts</p>
                </div>
              )}

              {formData.volumeTiers.map((tier, index) => (
                <div key={index} className="border border-gray-600 rounded-lg p-3 space-y-3 bg-gray-800">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-sm text-white">Tier {index + 1}</span>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newTiers = formData.volumeTiers.filter((_, i) => i !== index);
                        setFormData(prev => ({ ...prev, volumeTiers: newTiers }));
                      }}
                      className="border-gray-500 text-white hover:bg-gray-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-1">
                      <Label className="text-xs text-white">Min Quantity *</Label>
                      <Input
                        type="number"
                        min="1"
                        value={tier.minQuantity}
                        onChange={(e) => {
                          const newTiers = [...formData.volumeTiers];
                          newTiers[index] = { ...tier, minQuantity: e.target.value };
                          setFormData(prev => ({ ...prev, volumeTiers: newTiers }));
                        }}
                        placeholder="10"
                        className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                      />
                    </div>
                    <div className="space-y-1">
                      <Label className="text-xs text-white">Max Quantity (Optional)</Label>
                      <Input
                        type="number"
                        min="1"
                        value={tier.maxQuantity || ''}
                        onChange={(e) => {
                          const newTiers = [...formData.volumeTiers];
                          newTiers[index] = { ...tier, maxQuantity: e.target.value };
                          setFormData(prev => ({ ...prev, volumeTiers: newTiers }));
                        }}
                        placeholder="24"
                        className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-1">
                      <Label className="text-xs text-white">Discount Type *</Label>
                      <Select
                        value={tier.discountType}
                        onValueChange={(value) => {
                          const newTiers = [...formData.volumeTiers];
                          newTiers[index] = { ...tier, discountType: value as any };
                          setFormData(prev => ({ ...prev, volumeTiers: newTiers }));
                        }}
                      >
                        <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="PERCENTAGE">Percentage Off</SelectItem>
                          <SelectItem value="FIXED_AMOUNT">Fixed Amount Off</SelectItem>
                          <SelectItem value="FIXED_PRICE">Fixed Price Per Unit</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-1">
                      <Label className="text-xs text-white">
                        {tier.discountType === 'PERCENTAGE' ? 'Percentage (%)' :
                         tier.discountType === 'FIXED_AMOUNT' ? 'Amount Off ($)' :
                         'Price Per Unit ($)'}
                      </Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={tier.discountValue}
                        onChange={(e) => {
                          const newTiers = [...formData.volumeTiers];
                          newTiers[index] = { ...tier, discountValue: e.target.value };
                          setFormData(prev => ({ ...prev, volumeTiers: newTiers }));
                        }}
                        placeholder={tier.discountType === 'PERCENTAGE' ? '15' : '5.00'}
                        className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                      />
                    </div>
                  </div>
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  const newTier = {
                    minQuantity: '',
                    maxQuantity: '',
                    discountType: 'PERCENTAGE' as const,
                    discountValue: ''
                  };
                  setFormData(prev => ({ ...prev, volumeTiers: [...prev.volumeTiers, newTier] }));
                }}
                className="w-full border-gray-500 text-white hover:bg-gray-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Volume Tier
              </Button>
            </Card>
          )}

          <div className="grid grid-cols-2 gap-4">

            <div className="space-y-2">
              <Label htmlFor="usageLimit">Usage Limit</Label>
              <Input
                id="usageLimit"
                type="number"
                min="1"
                value={formData.usageLimit}
                onChange={(e) => handleInputChange('usageLimit', e.target.value)}
                placeholder="100"
              />
              <p className="text-xs text-muted-foreground">
                Current usage: {coupon.usageCount}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minOrderAmount">Minimum Order Amount ($)</Label>
              <Input
                id="minOrderAmount"
                type="number"
                step="0.01"
                min="0"
                value={formData.minOrderAmount}
                onChange={(e) => handleInputChange('minOrderAmount', e.target.value)}
                placeholder="50.00"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxDiscount">Maximum Discount ($)</Label>
              <Input
                id="maxDiscount"
                type="number"
                step="0.01"
                min="0"
                value={formData.maxDiscount}
                onChange={(e) => handleInputChange('maxDiscount', e.target.value)}
                placeholder="100.00"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startsAt">Start Date</Label>
              <Input
                id="startsAt"
                type="datetime-local"
                value={formData.startsAt}
                onChange={(e) => handleInputChange('startsAt', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="expiresAt">Expiry Date</Label>
              <Input
                id="expiresAt"
                type="datetime-local"
                value={formData.expiresAt}
                onChange={(e) => handleInputChange('expiresAt', e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => handleInputChange('isActive', checked)}
            />
            <Label htmlFor="isActive">Active</Label>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Coupon'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
