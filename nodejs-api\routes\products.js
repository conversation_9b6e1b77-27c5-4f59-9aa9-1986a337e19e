const express = require("express");
const { body, param, query } = require("express-validator");
const { PrismaClient } = require("@prisma/client");
const validateRequest = require("../middleware/validateRequest");
const { asyncHandler } = require("../middleware/errorHandler");
const { requireRole, requirePermission } = require("../middleware/auth");

const router = express.Router();
const prisma = new PrismaClient();

// Get all products with pagination and filters
router.get(
  "/",
  requirePermission("PRODUCTS", "READ"),
  [
    query("page")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Page must be a positive integer"),
    query("limit")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Limit must be between 1 and 100"),
    query("status")
      .optional()
      .isIn(["DRAFT", "ACTIVE", "INACTIVE", "ARCHIVED"])
      .withMessage("Invalid status"),
    query("search")
      .optional()
      .isString()
      .withMessage("Search must be a string"),
    query("category")
      .optional()
      .isString()
      .withMessage("Category must be a string"),
    query("tag")
      .optional()
      .isString()
      .withMessage("Tag must be a string"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const {
      page = 1,
      limit = 10,
      status,
      search,
      category,
      tag,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build where clause
    const where = {};
    if (status) where.status = status;
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }
    if (category) {
      where.categories = {
        some: {
          name: { contains: category, mode: "insensitive" },
        },
      };
    }
    if (tag) {
      where.tags = {
        some: {
          tag: { contains: tag, mode: "insensitive" },
        },
      };
    }

    // Get products and total count
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: parseInt(limit),
        orderBy: { [sortBy]: sortOrder },
        include: {
          variants: {
            select: {
              id: true,
              sku: true,
              name: true,
              description: true,
              regularPrice: true,
              salePrice: true,
              isActive: true,
              inventory: {
                select: {
                  quantity: true,
                  reservedQty: true,
                  locationId: true,
                },
              },
              segmentPrices: true,
              variantOptions: true,
            },
          },
          images: {
            select: {
              id: true,
              url: true,
              altText: true,
              sortOrder: true,
            },
            orderBy: { sortOrder: "asc" },
          },
          categories: {
            select: {
              id: true,
              name: true,
            },
          },
          tags: {
            select: {
              id: true,
              tag: true,
            },
          },
          _count: {
            select: {
              variants: true,
              reviews: true,
            },
          },
        },
      }),
      prisma.product.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit)),
        },
      },
    });
  })
);

// Get product by ID
router.get(
  "/:id",
  requirePermission("PRODUCTS", "READ"),
  [
    param("id").isString().withMessage("Product ID is required"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        variants: {
          include: {
            variantOptions: true,
            segmentPrices: true,
            inventory: {
              include: {
                location: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
        images: {
          orderBy: { sortOrder: "asc" },
        },
        categories: true,
        tags: true,
        relatedProducts: {
          include: {
            relatedProduct: {
              select: {
                id: true,
                name: true,
                status: true,
                images: {
                  select: {
                    url: true,
                    altText: true,
                  },
                  take: 1,
                  orderBy: { sortOrder: "asc" },
                },
              },
            },
          },
        },
        reviews: {
          include: {
            customer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
          take: 10,
        },
      },
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        error: "Product not found",
      });
    }

    res.json({
      success: true,
      data: product,
    });
  })
);

// Create new product
router.post(
  "/",
  requirePermission("PRODUCTS", "CREATE"),
  [
    body("name").notEmpty().trim().withMessage("Product name is required"),
    body("description")
      .optional()
      .isString()
      .withMessage("Description must be a string"),
    body("status")
      .optional()
      .isIn(["DRAFT", "ACTIVE", "INACTIVE", "ARCHIVED"])
      .withMessage("Invalid status"),
    body("categories")
      .optional()
      .isArray()
      .withMessage("Categories must be an array"),
    body("tags").optional().isArray().withMessage("Tags must be an array"),
    body("images").optional().isArray().withMessage("Images must be an array"),
    body("seoTitle").optional().isString().withMessage("SEO title must be a string"),
    body("seoDescription").optional().isString().withMessage("SEO description must be a string"),
    body("seoSlug").optional().isString().withMessage("SEO slug must be a string"),
    body("variants")
      .isArray({ min: 1 })
      .withMessage("At least one variant is required"),
    body("variants.*.sku")
      .notEmpty()
      .withMessage("SKU is required for each variant"),
    body("variants.*.name").notEmpty().withMessage("Variant name is required"),
    body("variants.*.regularPrice")
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Regular price must be a valid decimal"),
    body("variants.*.salePrice")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Sale price must be a valid decimal"),
    body("variants.*.weight")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Weight must be a valid decimal"),
    body("variants.*.hsn")
      .optional()
      .isString()
      .withMessage("HSN must be a string"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const {
      name,
      description,
      status = "DRAFT",
      categories = [],
      tags = [],
      images = [],
      variants,
      seoTitle,
      seoDescription,
      seoSlug,
    } = req.body;

    // Check if any variant SKU already exists
    const existingSkus = await prisma.productVariant.findMany({
      where: {
        sku: {
          in: variants.map((v) => v.sku),
        },
      },
      select: { sku: true },
    });

    if (existingSkus.length > 0) {
      return res.status(409).json({
        success: false,
        error: `SKU(s) already exist: ${existingSkus
          .map((s) => s.sku)
          .join(", ")}`,
      });
    }

    // Create product with variants in a transaction
    const product = await prisma.$transaction(async (tx) => {
      // Create product
      const newProduct = await tx.product.create({
        data: {
          name,
          description,
          status,
          seoTitle,
          seoDescription,
          seoSlug,
        },
      });

      // Create categories
      if (categories.length > 0) {
        await tx.productCategory.createMany({
          data: categories.map((cat) => ({
            productId: newProduct.id,
            name: cat,
          })),
        });
      }

      // Create tags
      if (tags.length > 0) {
        await tx.productTag.createMany({
          data: tags.map((tag) => ({
            productId: newProduct.id,
            tag,
          })),
        });
      }

      // Create images
      if (images.length > 0) {
        await tx.productImage.createMany({
          data: images.map((img, index) => ({
            productId: newProduct.id,
            url: img.url,
            altText: img.altText || "",
            sortOrder: img.sortOrder || index,
          })),
        });
      }

      // Create variants
      for (const variant of variants) {
        const newVariant = await tx.productVariant.create({
          data: {
            productId: newProduct.id,
            sku: variant.sku,
            name: variant.name,
            description: variant.description,
            regularPrice: variant.regularPrice,
            salePrice: variant.salePrice,
            weight: variant.weight,
            hsn: variant.hsn,
            seoTitle: variant.seoTitle,
            seoDescription: variant.seoDescription,
            seoSlug: variant.seoSlug,
            isActive: variant.isActive !== undefined ? variant.isActive : true,
          },
        });

        // Create variant options
        if (variant.options && variant.options.length > 0) {
          await tx.variantOption.createMany({
            data: variant.options.map((opt) => ({
              variantId: newVariant.id,
              name: opt.name,
              value: opt.value,
            })),
          });
        }
      }

      // Return complete product with relations
      return tx.product.findUnique({
        where: { id: newProduct.id },
        include: {
          variants: {
            include: {
              variantOptions: true,
            },
          },
          images: {
            orderBy: { sortOrder: "asc" },
          },
          categories: true,
          tags: true,
        },
      });
    });

    res.status(201).json({
      success: true,
      message: "Product created successfully",
      data: product,
    });
  })
);

// Update product
router.put(
  "/:id",
  requirePermission("PRODUCTS", "UPDATE"),
  [
    param("id").isString().withMessage("Product ID is required"),
    body("name")
      .optional()
      .notEmpty()
      .trim()
      .withMessage("Product name cannot be empty"),
    body("description")
      .optional()
      .isString()
      .withMessage("Description must be a string"),
    body("status")
      .optional()
      .isIn(["DRAFT", "ACTIVE", "INACTIVE", "ARCHIVED"])
      .withMessage("Invalid status"),
    body("categories")
      .optional()
      .isArray()
      .withMessage("Categories must be an array"),
    body("tags").optional().isArray().withMessage("Tags must be an array"),
    body("images").optional().isArray().withMessage("Images must be an array"),
    body("seoTitle").optional().isString().withMessage("SEO title must be a string"),
    body("seoDescription").optional().isString().withMessage("SEO description must be a string"),
    body("seoSlug").optional().isString().withMessage("SEO slug must be a string"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { name, description, status, categories, tags, images, seoTitle, seoDescription, seoSlug } = req.body;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        error: "Product not found",
      });
    }

    // Update product in a transaction
    const product = await prisma.$transaction(async (tx) => {
      // Update product basic info
      const updateData = {};
      if (name) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (status) updateData.status = status;
      if (seoTitle !== undefined) updateData.seoTitle = seoTitle;
      if (seoDescription !== undefined) updateData.seoDescription = seoDescription;
      if (seoSlug !== undefined) updateData.seoSlug = seoSlug;

      await tx.product.update({
        where: { id },
        data: updateData,
      });

      // Update categories
      if (categories) {
        await tx.productCategory.deleteMany({
          where: { productId: id },
        });

        if (categories.length > 0) {
          await tx.productCategory.createMany({
            data: categories.map((cat) => ({
              productId: id,
              name: cat,
            })),
          });
        }
      }

      // Update tags
      if (tags) {
        await tx.productTag.deleteMany({
          where: { productId: id },
        });

        if (tags.length > 0) {
          await tx.productTag.createMany({
            data: tags.map((tag) => ({
              productId: id,
              tag,
            })),
          });
        }
      }

      // Update images
      if (images) {
        await tx.productImage.deleteMany({
          where: { productId: id },
        });

        if (images.length > 0) {
          await tx.productImage.createMany({
            data: images.map((img, index) => ({
              productId: id,
              url: img.url,
              altText: img.altText || "",
              sortOrder: img.sortOrder || index,
            })),
          });
        }
      }

      // Return updated product with relations
      return tx.product.findUnique({
        where: { id },
        include: {
          variants: {
            include: {
              variantOptions: true,
            },
          },
          images: {
            orderBy: { sortOrder: "asc" },
          },
          categories: true,
          tags: true,
        },
      });
    });

    res.json({
      success: true,
      message: "Product updated successfully",
      data: product,
    });
  })
);

// Delete product
router.delete(
  "/:id",
  requirePermission("PRODUCTS", "DELETE"),
  [
    param("id").isString().withMessage("Product ID is required"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        error: "Product not found",
      });
    }

    // Soft delete by setting status to ARCHIVED
    await prisma.product.update({
      where: { id },
      data: { status: "ARCHIVED" },
    });

    res.json({
      success: true,
      message: "Product deleted successfully",
    });
  })
);

// Bulk delete products
router.post(
  "/bulk-delete",
  requirePermission("PRODUCTS", "DELETE"),
  [body("ids").isArray({ min: 1 }).withMessage("ids must be a non-empty array"), validateRequest],
  asyncHandler(async (req, res) => {
    const { ids } = req.body;
    // Soft delete by setting status to ARCHIVED
    const result = await prisma.product.updateMany({
      where: { id: { in: ids } },
      data: { status: "ARCHIVED" },
    });
    res.json({ success: true, deleted: result.count });
  })
);

// Bulk import products
router.post(
  "/bulk-import",
  requirePermission("PRODUCTS", "CREATE"),
  [body("products").isArray({ min: 1 }).withMessage("products must be a non-empty array"), validateRequest],
  asyncHandler(async (req, res) => {
    const { products } = req.body;
    let created = 0;
    await prisma.$transaction(async (tx) => {
      for (const p of products) {
        if (!p.name || !Array.isArray(p.variants) || p.variants.length === 0) continue;
        const mainVariant = p.variants[0] || p;
        if (!mainVariant.sku || !mainVariant.regularPrice) continue;
        await tx.product.create({
          data: {
            name: p.name,
            description: p.description || '',
            status: p.status || 'DRAFT',
            variants: {
              create: [{
                sku: mainVariant.sku,
                name: mainVariant.name || p.name,
                regularPrice: parseFloat(mainVariant.regularPrice),
                salePrice: mainVariant.salePrice ? parseFloat(mainVariant.salePrice) : undefined,
                isActive: true,
              }],
            },
          },
        });
        created++;
      }
    });
    res.json({ success: true, created });
  })
);

// Create variant for existing product
router.post(
  "/:id/variants",
  requirePermission("PRODUCTS", "CREATE"),
  [
    param("id").isString().withMessage("Product ID is required"),
    body("sku").notEmpty().withMessage("SKU is required"),
    body("name").notEmpty().withMessage("Variant name is required"),
    body("regularPrice")
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Regular price must be a valid decimal"),
    body("salePrice")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Sale price must be a valid decimal"),
    body("weight")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Weight must be a valid decimal"),
    body("hsn").optional().isString().withMessage("HSN must be a string"),
    body("options")
      .optional()
      .isArray()
      .withMessage("Options must be an array"),
    body("segmentPrices")
      .optional()
      .isArray()
      .withMessage("Segment prices must be an array"),
    body("segmentPrices.*.customerType")
      .optional()
      .isIn(["B2C", "B2B", "ENTERPRISE"])
      .withMessage("Invalid customer type"),
    body("segmentPrices.*.regularPrice")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Regular price must be a valid decimal"),
    body("segmentPrices.*.salePrice")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Sale price must be a valid decimal"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const {
      sku,
      name,
      description,
      regularPrice,
      salePrice,
      weight,
      hsn,
      seoTitle,
      seoDescription,
      seoSlug,
      isActive = true,
      options = [],
      segmentPrices = [],
    } = req.body;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        error: "Product not found",
      });
    }

    // Check if SKU already exists
    const existingSku = await prisma.productVariant.findUnique({
      where: { sku },
    });

    if (existingSku) {
      return res.status(409).json({
        success: false,
        error: "SKU already exists",
      });
    }

    // Create variant with segment prices
    const variant = await prisma.$transaction(async (tx) => {
      const newVariant = await tx.productVariant.create({
        data: {
          productId: id,
          sku,
          name,
          description,
          regularPrice,
          salePrice,
          weight,
          hsn,
          seoTitle,
          seoDescription,
          seoSlug,
          isActive,
          segmentPrices: {
            createMany: {
              data: segmentPrices.map((sp) => ({
                customerType: sp.customerType,
                regularPrice: sp.regularPrice,
                salePrice: sp.salePrice,
              })),
            },
          },
          variantOptions: {
            createMany: {
              data: options.map((opt) => ({
                name: opt.name,
                value: opt.value,
              })),
            },
          },
        },
      });

      return tx.productVariant.findUnique({
        where: { id: newVariant.id },
        include: {
          variantOptions: true,
          segmentPrices: true,
        },
      });
    });

    res.status(201).json({
      success: true,
      message: "Variant created successfully",
      data: variant,
    });
  })
);

// Update variant
router.put(
  "/:productId/variants/:variantId",
  requirePermission("PRODUCTS", "UPDATE"),
  [
    param("productId").isString().withMessage("Product ID is required"),
    param("variantId").isString().withMessage("Variant ID is required"),
    body("sku").optional().notEmpty().withMessage("SKU cannot be empty"),
    body("name")
      .optional()
      .notEmpty()
      .withMessage("Variant name cannot be empty"),
    body("regularPrice")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Regular price must be a valid decimal"),
    body("salePrice")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Sale price must be a valid decimal"),
    body("weight")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Weight must be a valid decimal"),
    body("hsn").optional().isString().withMessage("HSN must be a string"),
    body("isActive")
      .optional()
      .isBoolean()
      .withMessage("isActive must be boolean"),
    body("options")
      .optional()
      .isArray()
      .withMessage("Options must be an array"),
    body("segmentPrices")
      .optional()
      .isArray()
      .withMessage("Segment prices must be an array"),
    body("segmentPrices.*.customerType")
      .optional()
      .isIn(["B2C", "B2B", "ENTERPRISE"])
      .withMessage("Invalid customer type"),
    body("segmentPrices.*.regularPrice")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Regular price must be a valid decimal"),
    body("segmentPrices.*.salePrice")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Sale price must be a valid decimal"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { productId, variantId } = req.params;
    const {
      sku,
      name,
      description,
      regularPrice,
      salePrice,
      weight,
      hsn,
      seoTitle,
      seoDescription,
      seoSlug,
      isActive,
      options,
      segmentPrices,
    } = req.body;

    // Check if variant exists
    const existingVariant = await prisma.productVariant.findUnique({
      where: { id: variantId, productId },
    });

    if (!existingVariant) {
      return res.status(404).json({
        success: false,
        error: "Variant not found",
      });
    }

    // Check if new SKU already exists (if SKU is being updated)
    if (sku && sku !== existingVariant.sku) {
      const existingSku = await prisma.productVariant.findUnique({
        where: { sku },
      });

      if (existingSku) {
        return res.status(409).json({
          success: false,
          error: "SKU already exists",
        });
      }
    }

    // Update variant
    const variant = await prisma.$transaction(async (tx) => {
      // Update variant basic info
      const updateData = {};
      if (sku) updateData.sku = sku;
      if (name) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (regularPrice) updateData.regularPrice = regularPrice;
      if (salePrice !== undefined) updateData.salePrice = salePrice;
      if (weight !== undefined) updateData.weight = weight;
      if (hsn !== undefined) updateData.hsn = hsn;
      if (seoTitle !== undefined) updateData.seoTitle = seoTitle;
      if (seoDescription !== undefined)
        updateData.seoDescription = seoDescription;
      if (seoSlug !== undefined) updateData.seoSlug = seoSlug;
      if (isActive !== undefined) updateData.isActive = isActive;

      await tx.productVariant.update({
        where: { id: variantId },
        data: updateData,
      });

      // Update options
      if (options) {
        await tx.variantOption.deleteMany({
          where: { variantId },
        });

        if (options.length > 0) {
          await tx.variantOption.createMany({
            data: options.map((opt) => ({
              variantId,
              name: opt.name,
              value: opt.value,
            })),
          });
        }
      }

      // Update segment prices
      if (segmentPrices) {
        await tx.segmentPrice.deleteMany({
          where: { variantId },
        });

        if (segmentPrices.length > 0) {
          await tx.segmentPrice.createMany({
            data: segmentPrices.map((sp) => ({
              variantId,
              customerType: sp.customerType,
              regularPrice: sp.regularPrice,
              salePrice: sp.salePrice,
            })),
          });
        }
      }

      return tx.productVariant.findUnique({
        where: { id: variantId },
        include: {
          variantOptions: true,
          segmentPrices: true,
        },
      });
    });

    res.json({
      success: true,
      message: "Variant updated successfully",
      data: variant,
    });
  })
);

// Delete variant
router.delete(
  "/:productId/variants/:variantId",
  requirePermission("PRODUCTS", "DELETE"),
  [
    param("productId").isString().withMessage("Product ID is required"),
    param("variantId").isString().withMessage("Variant ID is required"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { productId, variantId } = req.params;

    // Check if variant exists
    const existingVariant = await prisma.productVariant.findUnique({
      where: { id: variantId, productId },
    });

    if (!existingVariant) {
      return res.status(404).json({
        success: false,
        error: "Variant not found",
      });
    }

    // Soft delete by setting isActive to false
    await prisma.productVariant.update({
      where: { id: variantId },
      data: { isActive: false },
    });

    res.json({
      success: true,
      message: "Variant deleted successfully",
    });
  })
);

// List tags for a product
router.get(
  '/:id/tags',
  requirePermission('PRODUCTS', 'READ'),
  [param('id').isString().withMessage('Product ID is required'), validateRequest],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const product = await prisma.product.findUnique({
      where: { id },
      include: { tags: true },
    });
    if (!product) {
      return res.status(404).json({ success: false, error: 'Product not found' });
    }
    res.json({ success: true, data: product.tags });
  })
);
// Add a tag to a product
router.post(
  '/:id/tags',
  requirePermission('PRODUCTS', 'UPDATE'),
  [param('id').isString().withMessage('Product ID is required'), body('tag').isString().notEmpty().withMessage('Tag is required'), validateRequest],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { tag } = req.body;
    const product = await prisma.product.findUnique({ where: { id } });
    if (!product) {
      return res.status(404).json({ success: false, error: 'Product not found' });
    }
    const newTag = await prisma.productTag.create({ data: { productId: id, tag } });
    res.status(201).json({ success: true, data: newTag });
  })
);
// Remove a tag from a product
router.delete(
  '/:id/tags/:tagId',
  requirePermission('PRODUCTS', 'UPDATE'),
  [param('id').isString().withMessage('Product ID is required'), param('tagId').isString().withMessage('Tag ID is required'), validateRequest],
  asyncHandler(async (req, res) => {
    const { tagId } = req.params;
    await prisma.productTag.delete({ where: { id: tagId } });
    res.json({ success: true });
  })
);

// Product Relations Management
router.post(
  "/:id/relations",
  requirePermission("PRODUCTS", "UPDATE"),
  [
    param("id").isString().withMessage("Product ID is required"),
    body("relatedProductId").isString().withMessage("Related product ID is required"),
    body("type").isIn(["RELATED", "UPSELL", "CROSS_SELL"]).withMessage("Invalid relation type"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { relatedProductId, type } = req.body;

    // Check if product exists
    const product = await prisma.product.findUnique({ where: { id } });
    if (!product) {
      return res.status(404).json({
        success: false,
        error: "Product not found",
      });
    }

    // Check if related product exists
    const relatedProduct = await prisma.product.findUnique({ where: { id: relatedProductId } });
    if (!relatedProduct) {
      return res.status(404).json({
        success: false,
        error: "Related product not found",
      });
    }

    // Check if relation already exists
    const existingRelation = await prisma.productRelation.findUnique({
      where: {
        productId_relatedProductId_type: {
          productId: id,
          relatedProductId,
          type,
        },
      },
    });

    if (existingRelation) {
      return res.status(409).json({
        success: false,
        error: "Relation already exists",
      });
    }

    // Create relation
    const relation = await prisma.productRelation.create({
      data: {
        productId: id,
        relatedProductId,
        type,
      },
      include: {
        relatedProduct: {
          select: {
            id: true,
            name: true,
            status: true,
            images: {
              select: {
                url: true,
                altText: true,
              },
              take: 1,
              orderBy: { sortOrder: "asc" },
            },
          },
        },
      },
    });

    res.json({
      success: true,
      message: "Product relation created successfully",
      data: relation,
    });
  })
);

router.delete(
  "/:productId/relations/:relationId",
  requirePermission("PRODUCTS", "UPDATE"),
  [
    param("productId").isString().withMessage("Product ID is required"),
    param("relationId").isString().withMessage("Relation ID is required"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { productId, relationId } = req.params;

    // Check if relation exists and belongs to the product
    const relation = await prisma.productRelation.findUnique({
      where: { id: relationId, productId },
    });

    if (!relation) {
      return res.status(404).json({
        success: false,
        error: "Relation not found",
      });
    }

    // Delete relation
    await prisma.productRelation.delete({
      where: { id: relationId },
    });

    res.json({
      success: true,
      message: "Product relation deleted successfully",
    });
  })
);

router.get(
  "/:id/relations",
  requirePermission("PRODUCTS", "READ"),
  [
    param("id").isString().withMessage("Product ID is required"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    const relations = await prisma.productRelation.findMany({
      where: { productId: id },
      include: {
        relatedProduct: {
          select: {
            id: true,
            name: true,
            status: true,
            images: {
              select: {
                url: true,
                altText: true,
              },
              take: 1,
              orderBy: { sortOrder: "asc" },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    res.json({
      success: true,
      data: relations,
    });
  })
);

module.exports = router;
