const express = require('express');
const { body, param, query } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const validateRequest = require('../middleware/validateRequest');
const { asyncHandler } = require('../middleware/errorHandler');
const { requireRole, requirePermission } = require('../middleware/auth');
const { calculatePromotionDiscount } = require('../utils/promotionCalculator');

const router = express.Router();
const prisma = new PrismaClient();

// Get all promotions
router.get('/', requirePermission('PROMOTIONS', 'READ'), [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { page = 1, limit = 20, isActive } = req.query;
  const skip = (parseInt(page) - 1) * parseInt(limit);
  const where = {};
  if (isActive !== undefined) where.isActive = isActive === 'true';
  const [promotions, total] = await Promise.all([
    prisma.promotion.findMany({
      where,
      skip: parseInt(skip),
      take: parseInt(limit),
      orderBy: { createdAt: 'desc' },
    }),
    prisma.promotion.count({ where })
  ]);
  res.json({
    success: true,
    data: promotions,
    pagination: { page: parseInt(page), limit: parseInt(limit), total, pages: Math.ceil(total / parseInt(limit)) }
  });
}));

// Get promotion by code (validate coupon)
router.get('/code/:code', requirePermission('PROMOTIONS', 'READ'), [
  param('code').notEmpty().withMessage('Promotion code is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { code } = req.params;
  const now = new Date();
  const promo = await prisma.promotion.findFirst({
    where: {
      code: code.toUpperCase(),
      isActive: true,
      OR: [
        { startsAt: null },
        { startsAt: { lte: now } }
      ],
      AND: [
        {
          OR: [
            { expiresAt: null },
            { expiresAt: { gte: now } }
          ]
        }
      ]
    },
  });

  // Additional check for usage limit
  if (promo && promo.usageLimit && promo.usageCount >= promo.usageLimit) {
    return res.status(404).json({ success: false, error: 'Coupon usage limit exceeded' });
  }
  if (!promo) {
    return res.status(404).json({ success: false, error: 'Invalid or expired coupon code' });
  }
  res.json({ success: true, data: promo });
}));

// Get single promotion by ID with all related data
router.get('/:id', requirePermission('PROMOTIONS', 'READ'), [
  param('id').notEmpty().withMessage('Promotion ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;

  const promotion = await prisma.promotion.findUnique({
    where: { id },
    include: {
      productRules: {
        include: {
          product: { select: { name: true } },
          variant: { select: { name: true, sku: true } }
        }
      },
      categoryRules: {
        include: {
          category: { select: { name: true } }
        }
      },
      volumeTiers: {
        orderBy: { minQuantity: 'asc' }
      },
      _count: {
        select: { usageHistory: true }
      }
    }
  });

  if (!promotion) {
    return res.status(404).json({ success: false, error: 'Promotion not found' });
  }

  res.json({ success: true, data: promotion });
}));

// Create promotion
router.post('/', requirePermission('PROMOTIONS', 'CREATE'), [
  body('code').notEmpty().withMessage('Promotion code is required'),
  body('name').notEmpty().withMessage('Promotion name is required'),
  body('type').isIn(['PERCENTAGE', 'FIXED_AMOUNT', 'FREE_SHIPPING', 'BOGO', 'VOLUME_DISCOUNT']).withMessage('Invalid promotion type'),
  body('value').isDecimal({ decimal_digits: '0,2' }).withMessage('Value must be a valid decimal'),
  body('customerTypes').optional().isArray().withMessage('Customer types must be an array'),
  body('bogoType').optional().isIn(['BUY_X_GET_Y_FREE', 'BUY_X_GET_Y_PERCENT', 'BUY_X_GET_Y_FIXED', 'CHEAPEST_FREE', 'MOST_EXPENSIVE_FREE']).withMessage('Invalid BOGO type'),
  body('buyQuantity').optional().isInt({ min: 1 }).withMessage('Buy quantity must be a positive integer'),
  body('getQuantity').optional().isInt({ min: 1 }).withMessage('Get quantity must be a positive integer'),
  body('getDiscount').optional().isDecimal({ decimal_digits: '0,2' }).withMessage('Get discount must be a valid decimal'),
  body('productRules').optional().isArray().withMessage('Product rules must be an array'),
  body('categoryRules').optional().isArray().withMessage('Category rules must be an array'),
  body('volumeTiers').optional().isArray().withMessage('Volume tiers must be an array'),
  validateRequest
], asyncHandler(async (req, res) => {
  const {
    code, name, description, type, value, minOrderAmount, maxDiscount, usageLimit, startsAt, expiresAt,
    customerTypes, bogoType, buyQuantity, getQuantity, getDiscount,
    productRules, categoryRules, volumeTiers
  } = req.body;

  const result = await prisma.$transaction(async (prisma) => {
    // Create the promotion
    const promo = await prisma.promotion.create({
      data: {
        code: code.toUpperCase(),
        name,
        description,
        type,
        value: parseFloat(value),
        minOrderAmount: minOrderAmount ? parseFloat(minOrderAmount) : null,
        maxDiscount: maxDiscount ? parseFloat(maxDiscount) : null,
        usageLimit: usageLimit ? parseInt(usageLimit) : null,
        startsAt: startsAt ? new Date(startsAt) : null,
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        customerTypes: customerTypes || [],
        bogoType: bogoType || null,
        buyQuantity: buyQuantity ? parseInt(buyQuantity) : null,
        getQuantity: getQuantity ? parseInt(getQuantity) : null,
        getDiscount: getDiscount ? parseFloat(getDiscount) : null,
        isActive: true,
      },
    });

    // Create product rules
    if (productRules && productRules.length > 0) {
      await prisma.promotionProductRule.createMany({
        data: productRules.map(rule => ({
          promotionId: promo.id,
          productId: rule.productId || null,
          variantId: rule.variantId || null,
          ruleType: rule.ruleType,
          quantity: rule.quantity ? parseInt(rule.quantity) : null,
        }))
      });
    }

    // Create category rules
    if (categoryRules && categoryRules.length > 0) {
      await prisma.promotionCategoryRule.createMany({
        data: categoryRules.map(rule => ({
          promotionId: promo.id,
          categoryId: rule.categoryId,
          ruleType: rule.ruleType,
        }))
      });
    }

    // Create volume tiers
    if (volumeTiers && volumeTiers.length > 0) {
      await prisma.promotionVolumeTier.createMany({
        data: volumeTiers.map(tier => ({
          promotionId: promo.id,
          minQuantity: parseInt(tier.minQuantity),
          maxQuantity: tier.maxQuantity ? parseInt(tier.maxQuantity) : null,
          discountType: tier.discountType,
          discountValue: parseFloat(tier.discountValue),
        }))
      });
    }

    return promo;
  });

  res.status(201).json({ success: true, data: result });
}));

// Update promotion
router.put('/:id', requirePermission('PROMOTIONS', 'UPDATE'), [
  param('id').isString().withMessage('Promotion ID is required'),
  body('name').optional().notEmpty().withMessage('Promotion name cannot be empty'),
  body('description').optional().isString().withMessage('Description must be a string'),
  body('type').optional().isIn(['PERCENTAGE', 'FIXED_AMOUNT', 'FREE_SHIPPING', 'BOGO', 'VOLUME_DISCOUNT']).withMessage('Invalid promotion type'),
  body('value').optional().isDecimal({ decimal_digits: '0,2' }).withMessage('Value must be a valid decimal'),
  body('minOrderAmount').optional().isDecimal({ decimal_digits: '0,2' }).withMessage('Min order amount must be a valid decimal'),
  body('maxDiscount').optional().isDecimal({ decimal_digits: '0,2' }).withMessage('Max discount must be a valid decimal'),
  body('usageLimit').optional().isInt({ min: 0 }).withMessage('Usage limit must be a positive integer'),
  body('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
  body('startsAt').optional().isISO8601().withMessage('Start date must be a valid date'),
  body('expiresAt').optional().isISO8601().withMessage('Expiry date must be a valid date'),
  body('customerTypes').optional().isArray().withMessage('Customer types must be an array'),
  body('bogoType').optional().isIn(['BUY_X_GET_Y_FREE', 'BUY_X_GET_Y_PERCENT', 'BUY_X_GET_Y_FIXED', 'CHEAPEST_FREE', 'MOST_EXPENSIVE_FREE']).withMessage('Invalid BOGO type'),
  body('buyQuantity').optional().isInt({ min: 1 }).withMessage('Buy quantity must be a positive integer'),
  body('getQuantity').optional().isInt({ min: 1 }).withMessage('Get quantity must be a positive integer'),
  body('getDiscount').optional().isDecimal({ decimal_digits: '0,2' }).withMessage('Get discount must be a valid decimal'),
  body('productRules').optional().isArray().withMessage('Product rules must be an array'),
  body('categoryRules').optional().isArray().withMessage('Category rules must be an array'),
  body('volumeTiers').optional().isArray().withMessage('Volume tiers must be an array'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    name, description, type, value, minOrderAmount, maxDiscount, usageLimit, isActive, startsAt, expiresAt,
    customerTypes, bogoType, buyQuantity, getQuantity, getDiscount,
    productRules, categoryRules, volumeTiers
  } = req.body;

  const result = await prisma.$transaction(async (prisma) => {
    // Prepare update data for main promotion
    const updateData = {};

    // Basic fields
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (type !== undefined) updateData.type = type;
    if (value !== undefined) updateData.value = parseFloat(value);
    if (minOrderAmount !== undefined) updateData.minOrderAmount = minOrderAmount ? parseFloat(minOrderAmount) : null;
    if (maxDiscount !== undefined) updateData.maxDiscount = maxDiscount ? parseFloat(maxDiscount) : null;
    if (usageLimit !== undefined) updateData.usageLimit = usageLimit ? parseInt(usageLimit) : null;
    if (isActive !== undefined) updateData.isActive = isActive;
    if (startsAt !== undefined) updateData.startsAt = startsAt ? new Date(startsAt) : null;
    if (expiresAt !== undefined) updateData.expiresAt = expiresAt ? new Date(expiresAt) : null;

    // Advanced fields
    if (customerTypes !== undefined) updateData.customerTypes = customerTypes;
    if (bogoType !== undefined) updateData.bogoType = bogoType || null;
    if (buyQuantity !== undefined) updateData.buyQuantity = buyQuantity ? parseInt(buyQuantity) : null;
    if (getQuantity !== undefined) updateData.getQuantity = getQuantity ? parseInt(getQuantity) : null;
    if (getDiscount !== undefined) updateData.getDiscount = getDiscount ? parseFloat(getDiscount) : null;

    // Update main promotion
    const promotion = await prisma.promotion.update({
      where: { id },
      data: updateData,
    });

    // Update volume tiers
    if (volumeTiers !== undefined) {
      // Delete existing tiers
      await prisma.promotionVolumeTier.deleteMany({
        where: { promotionId: id }
      });

      // Create new tiers
      if (volumeTiers.length > 0) {
        await prisma.promotionVolumeTier.createMany({
          data: volumeTiers.map(tier => ({
            promotionId: id,
            minQuantity: parseInt(tier.minQuantity),
            maxQuantity: tier.maxQuantity ? parseInt(tier.maxQuantity) : null,
            discountType: tier.discountType,
            discountValue: parseFloat(tier.discountValue),
          }))
        });
      }
    }

    return promotion;
  });

  res.json({
    success: true,
    data: result,
    message: 'Promotion updated successfully'
  });
}));

// Delete promotion
router.delete('/:id', requirePermission('PROMOTIONS', 'DELETE'), [
  param('id').isString().withMessage('Promotion ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if promotion exists
  const promotion = await prisma.promotion.findUnique({
    where: { id }
  });

  if (!promotion) {
    return res.status(404).json({
      success: false,
      error: 'Promotion not found'
    });
  }

  // Delete the promotion
  await prisma.promotion.delete({
    where: { id }
  });

  res.json({
    success: true,
    message: 'Promotion deleted successfully'
  });
}));

// Update promotion usage (increment usageCount)
router.post('/use/:code', requirePermission('PROMOTIONS', 'UPDATE'), [
  param('code').notEmpty().withMessage('Promotion code is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { code } = req.params;
  const promo = await prisma.promotion.update({
    where: { code: code.toUpperCase() },
    data: { usageCount: { increment: 1 } },
  });
  res.json({ success: true, data: promo });
}));

// Calculate promotion discount
router.post('/calculate-discount', requirePermission('PROMOTIONS', 'READ'), [
  body('promotionCode').notEmpty().withMessage('Promotion code is required'),
  body('orderItems').isArray().withMessage('Order items must be an array'),
  body('subtotal').isNumeric().withMessage('Subtotal must be a number'),
  body('shippingAmount').isNumeric().withMessage('Shipping amount must be a number'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { promotionCode, orderItems, customerId, subtotal, shippingAmount } = req.body;

  // Get promotion with all related data
  const promotion = await prisma.promotion.findFirst({
    where: {
      code: promotionCode.toUpperCase(),
      isActive: true
    },
    include: {
      productRules: {
        include: {
          product: true,
          variant: true
        }
      },
      categoryRules: {
        include: {
          category: true
        }
      },
      volumeTiers: {
        orderBy: { minQuantity: 'asc' }
      }
    }
  });

  if (!promotion) {
    return res.status(404).json({ success: false, error: 'Promotion not found' });
  }

  // Get customer details if customerId provided
  let customer = null;
  if (customerId) {
    customer = await prisma.customer.findUnique({
      where: { id: customerId },
      select: { customerType: true }
    });
  }

  // Calculate discount using the promotion calculator
  const discountResult = await calculatePromotionDiscount(
    promotion,
    orderItems,
    customer,
    parseFloat(subtotal),
    parseFloat(shippingAmount)
  );

  res.json({ success: true, data: discountResult });
}));

// Test BOGO calculation endpoint
router.post('/test-bogo', requirePermission('PROMOTIONS', 'READ'), asyncHandler(async (req, res) => {
  const testPromotion = {
    id: 'test',
    code: 'TESTBOGO',
    type: 'BOGO',
    bogoType: 'BUY_X_GET_Y_FREE',
    buyQuantity: 2,
    getQuantity: 1,
    getDiscount: null,
    customerTypes: []
  };

  const testOrderItems = [
    { variantId: 'var1', quantity: 3, unitPrice: 10.00 },
    { variantId: 'var2', quantity: 2, unitPrice: 15.00 }
  ];

  const testCustomer = { customerType: 'RETAIL' };

  console.log('🧪 Testing BOGO calculation...');
  const result = await calculatePromotionDiscount(testPromotion, testOrderItems, testCustomer, 60.00, 5.00);

  res.json({
    success: true,
    data: {
      testPromotion,
      testOrderItems,
      result
    }
  });
}));

module.exports = router;