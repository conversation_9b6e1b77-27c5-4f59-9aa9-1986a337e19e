"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    FileText,
    Image,
    Video,
    Upload,
    Eye,
    Edit,
    Trash2,
    Plus,
    MoreHorizontal,
    Search,
    Globe,
    Calendar,
    User
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

// Mock data for pages
const pages = [
    {
        id: 1,
        title: "About Us",
        slug: "/about",
        status: "Published",
        type: "Static Page",
        lastModified: "2024-06-15",
        author: "Admin",
        views: 1247,
    },
    {
        id: 2,
        title: "Contact Us",
        slug: "/contact",
        status: "Published",
        type: "Static Page",
        lastModified: "2024-06-10",
        author: "Admin",
        views: 856,
    },
    {
        id: 3,
        title: "Privacy Policy",
        slug: "/privacy",
        status: "Published",
        type: "Legal Page",
        lastModified: "2024-06-08",
        author: "Legal Team",
        views: 234,
    },
    {
        id: 4,
        title: "Terms of Service",
        slug: "/terms",
        status: "Published",
        type: "Legal Page",
        lastModified: "2024-06-08",
        author: "Legal Team",
        views: 189,
    },
    {
        id: 5,
        title: "Research Blog - New Peptide Discoveries",
        slug: "/blog/new-peptide-discoveries",
        status: "Published",
        type: "Blog Post",
        lastModified: "2024-06-12",
        author: "Dr. Sarah Wilson",
        views: 2156,
    },
    {
        id: 6,
        title: "FAQ",
        slug: "/faq",
        status: "Draft",
        type: "Static Page",
        lastModified: "2024-06-16",
        author: "Admin",
        views: 0,
    },
];

// Mock data for media
const mediaFiles = [
    {
        id: 1,
        name: "peptide-research-lab.jpg",
        type: "image",
        size: "2.4 MB",
        dimensions: "1920x1080",
        uploadDate: "2024-06-15",
        url: "/media/peptide-research-lab.jpg",
    },
    {
        id: 2,
        name: "product-showcase.mp4",
        type: "video",
        size: "15.2 MB",
        duration: "2:45",
        uploadDate: "2024-06-12",
        url: "/media/product-showcase.mp4",
    },
    {
        id: 3,
        name: "company-logo.png",
        type: "image",
        size: "156 KB",
        dimensions: "512x512",
        uploadDate: "2024-06-10",
        url: "/media/company-logo.png",
    },
    {
        id: 4,
        name: "research-presentation.pdf",
        type: "document",
        size: "4.8 MB",
        pages: 24,
        uploadDate: "2024-06-08",
        url: "/media/research-presentation.pdf",
    },
];

const StatusBadge = ({ status }: { status: string }) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
        Published: "default",
        Draft: "secondary",
        Archived: "outline",
    };

    return <Badge variant={variants[status]}>{status}</Badge>;
};

const TypeBadge = ({ type }: { type: string }) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
        "Static Page": "outline",
        "Blog Post": "default",
        "Legal Page": "secondary",
    };

    return <Badge variant={variants[type]}>{type}</Badge>;
};

const MediaIcon = ({ type }: { type: string }) => {
    switch (type) {
        case "image":
            return <Image className="h-4 w-4" />;
        case "video":
            return <Video className="h-4 w-4" />;
        case "document":
            return <FileText className="h-4 w-4" />;
        default:
            return <FileText className="h-4 w-4" />;
    }
};

export function ContentContent() {
    const [searchTerm, setSearchTerm] = useState("");

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Content Management</h1>
                    <p className="text-muted-foreground">
                        Manage your website content, pages, and media assets.
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline">
                        <Eye className="h-4 w-4 mr-2" />
                        Preview Site
                    </Button>
                    <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Page
                    </Button>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Pages</CardTitle>
                        <FileText className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">24</div>
                        <p className="text-xs text-muted-foreground">
                            +2 new this month
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Published</CardTitle>
                        <Globe className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">18</div>
                        <p className="text-xs text-muted-foreground">
                            6 drafts remaining
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                        <Eye className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">12,486</div>
                        <p className="text-xs text-muted-foreground">
                            +18.2% from last month
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Media Files</CardTitle>
                        <Image className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">156</div>
                        <p className="text-xs text-muted-foreground">
                            8.2 GB total storage
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Tabs */}
            <Tabs defaultValue="pages" className="space-y-6">
                <TabsList>
                    <TabsTrigger value="pages">Pages</TabsTrigger>
                    <TabsTrigger value="media">Media Library</TabsTrigger>
                    <TabsTrigger value="menus">Navigation</TabsTrigger>
                    <TabsTrigger value="seo">SEO Settings</TabsTrigger>
                </TabsList>

                {/* Pages Tab */}
                <TabsContent value="pages" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle>Website Pages</CardTitle>
                                    <CardDescription>
                                        Manage your website pages and content
                                    </CardDescription>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="relative">
                                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            placeholder="Search pages..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="pl-8 w-64"
                                        />
                                    </div>
                                    <Select>
                                        <SelectTrigger className="w-32">
                                            <SelectValue placeholder="Filter" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Pages</SelectItem>
                                            <SelectItem value="published">Published</SelectItem>
                                            <SelectItem value="draft">Drafts</SelectItem>
                                            <SelectItem value="archived">Archived</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Title</TableHead>
                                        <TableHead>URL</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Views</TableHead>
                                        <TableHead>Author</TableHead>
                                        <TableHead>Last Modified</TableHead>
                                        <TableHead></TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {pages.map((page) => (
                                        <TableRow key={page.id}>
                                            <TableCell className="font-medium">{page.title}</TableCell>
                                            <TableCell>
                                                <code className="text-sm bg-muted px-2 py-1 rounded">
                                                    {page.slug}
                                                </code>
                                            </TableCell>
                                            <TableCell>
                                                <TypeBadge type={page.type} />
                                            </TableCell>
                                            <TableCell>
                                                <StatusBadge status={page.status} />
                                            </TableCell>
                                            <TableCell>{page.views.toLocaleString()}</TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Avatar className="h-6 w-6">
                                                        <AvatarImage src="/avatars/01.png" />
                                                        <AvatarFallback>
                                                            {page.author.split(" ").map(n => n[0]).join("")}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <span className="text-sm">{page.author}</span>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-1">
                                                    <Calendar className="h-4 w-4" />
                                                    {page.lastModified}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" className="h-8 w-8 p-0">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem>
                                                            <Edit className="h-4 w-4 mr-2" />
                                                            Edit Page
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <Eye className="h-4 w-4 mr-2" />
                                                            Preview
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <Globe className="h-4 w-4 mr-2" />
                                                            View Live
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem className="text-red-600">
                                                            <Trash2 className="h-4 w-4 mr-2" />
                                                            Delete Page
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Media Library Tab */}
                <TabsContent value="media" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-3">
                        {/* Media Gallery */}
                        <Card className="md:col-span-2">
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle>Media Library</CardTitle>
                                    <Button>
                                        <Upload className="h-4 w-4 mr-2" />
                                        Upload Files
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {mediaFiles.map((file) => (
                                        <div key={file.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                            <div className="flex items-center gap-3 mb-2">
                                                <MediaIcon type={file.type} />
                                                <div className="flex-1 min-w-0">
                                                    <p className="font-medium truncate">{file.name}</p>
                                                    <p className="text-sm text-muted-foreground">{file.size}</p>
                                                </div>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem>
                                                            <Eye className="h-4 w-4 mr-2" />
                                                            View
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <Edit className="h-4 w-4 mr-2" />
                                                            Edit
                                                        </DropdownMenuItem>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem className="text-red-600">
                                                            <Trash2 className="h-4 w-4 mr-2" />
                                                            Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </div>
                                            <div className="text-xs text-muted-foreground">
                                                {file.dimensions && <div>Dimensions: {file.dimensions}</div>}
                                                {file.duration && <div>Duration: {file.duration}</div>}
                                                {file.pages && <div>Pages: {file.pages}</div>}
                                                <div>Uploaded: {file.uploadDate}</div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Upload Area */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Quick Upload</CardTitle>
                                <CardDescription>
                                    Drag and drop files here
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                                    <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                                    <p className="text-sm text-muted-foreground mb-4">
                                        Drag files here or click to browse
                                    </p>
                                    <Button variant="outline">
                                        Choose Files
                                    </Button>
                                </div>

                                <div className="mt-6 space-y-4">
                                    <div>
                                        <Label htmlFor="alt-text">Alt Text</Label>
                                        <Input id="alt-text" placeholder="Describe the image..." />
                                    </div>
                                    <div>
                                        <Label htmlFor="caption">Caption</Label>
                                        <Input id="caption" placeholder="Optional caption..." />
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Switch id="public" />
                                        <Label htmlFor="public">Make public</Label>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* Navigation Tab */}
                <TabsContent value="menus" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Main Navigation</CardTitle>
                                <CardDescription>
                                    Configure your website's main navigation menu
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <span>Home</span>
                                        <div className="flex items-center gap-2">
                                            <Button variant="ghost" size="sm">
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button variant="ghost" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <span>Products</span>
                                        <div className="flex items-center gap-2">
                                            <Button variant="ghost" size="sm">
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button variant="ghost" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <span>About</span>
                                        <div className="flex items-center gap-2">
                                            <Button variant="ghost" size="sm">
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button variant="ghost" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <span>Contact</span>
                                        <div className="flex items-center gap-2">
                                            <Button variant="ghost" size="sm">
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button variant="ghost" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                                <Button className="w-full">
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Menu Item
                                </Button>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Footer Navigation</CardTitle>
                                <CardDescription>
                                    Configure your website's footer links
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <span>Privacy Policy</span>
                                        <div className="flex items-center gap-2">
                                            <Button variant="ghost" size="sm">
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button variant="ghost" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <span>Terms of Service</span>
                                        <div className="flex items-center gap-2">
                                            <Button variant="ghost" size="sm">
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button variant="ghost" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <span>FAQ</span>
                                        <div className="flex items-center gap-2">
                                            <Button variant="ghost" size="sm">
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button variant="ghost" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                                <Button className="w-full">
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Footer Link
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* SEO Settings Tab */}
                <TabsContent value="seo" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Global SEO Settings</CardTitle>
                                <CardDescription>
                                    Configure default SEO settings for your website
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="site-title">Site Title</Label>
                                    <Input
                                        id="site-title"
                                        placeholder="Centre Research - Premium Peptides"
                                        defaultValue="Centre Research - Premium Peptides"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="site-description">Site Description</Label>
                                    <Textarea
                                        id="site-description"
                                        placeholder="High-quality peptides for research and development..."
                                        rows={3}
                                        defaultValue="High-quality peptides for research and development. Trusted by researchers worldwide for over 10 years."
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="keywords">Keywords</Label>
                                    <Input
                                        id="keywords"
                                        placeholder="peptides, research, biotech, laboratory"
                                        defaultValue="peptides, research, biotech, laboratory, synthesis"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="og-image">Open Graph Image</Label>
                                    <div className="flex gap-2">
                                        <Input id="og-image" placeholder="URL to default OG image" />
                                        <Button variant="outline">
                                            <Upload className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Analytics & Tracking</CardTitle>
                                <CardDescription>
                                    Configure tracking codes and analytics
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="google-analytics">Google Analytics ID</Label>
                                    <Input
                                        id="google-analytics"
                                        placeholder="GA-XXXXXXXXX-X"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="gtm">Google Tag Manager ID</Label>
                                    <Input
                                        id="gtm"
                                        placeholder="GTM-XXXXXXX"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="facebook-pixel">Facebook Pixel ID</Label>
                                    <Input
                                        id="facebook-pixel"
                                        placeholder="Facebook Pixel ID"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="custom-head">Custom Head Code</Label>
                                    <Textarea
                                        id="custom-head"
                                        placeholder="Additional head tags..."
                                        rows={3}
                                    />
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch id="search-engine-indexing" defaultChecked />
                                    <Label htmlFor="search-engine-indexing">Allow search engine indexing</Label>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
}
