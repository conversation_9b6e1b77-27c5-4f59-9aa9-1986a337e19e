"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    BarChart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    LineChart,
    Line,
    PieChart,
    Pie,
    Cell,
    AreaChart,
    Area
} from "recharts";
import {
    TrendingUp,
    TrendingDown,
    DollarSign,
    ShoppingCart,
    Users,
    Package,
    Download,
    Calendar,
    Target,
    Globe,
    Star
} from "lucide-react";

// Mock data for analytics
const salesData = [
    { month: "Jan", revenue: 12400, orders: 145, customers: 89 },
    { month: "Feb", revenue: 15600, orders: 178, customers: 102 },
    { month: "Mar", revenue: 18900, orders: 203, customers: 123 },
    { month: "Apr", revenue: 16200, orders: 187, customers: 98 },
    { month: "May", revenue: 21500, orders: 234, customers: 145 },
    { month: "Jun", revenue: 25800, orders: 278, customers: 167 },
];

const dailySalesData = [
    { day: "Mon", revenue: 4200, orders: 28 },
    { day: "Tue", revenue: 3800, orders: 24 },
    { day: "Wed", revenue: 5100, orders: 35 },
    { day: "Thu", revenue: 4600, orders: 31 },
    { day: "Fri", revenue: 5800, orders: 39 },
    { day: "Sat", revenue: 3200, orders: 22 },
    { day: "Sun", revenue: 2400, orders: 16 },
];

const topProductsData = [
    { name: "Peptide Alpha-1", sales: 234, revenue: 29250, growth: 15.2 },
    { name: "Peptide Beta-2", sales: 189, revenue: 24570, growth: 8.7 },
    { name: "Peptide Gamma-3", sales: 156, revenue: 19500, growth: -2.3 },
    { name: "Peptide Delta-4", sales: 142, revenue: 18330, growth: 22.1 },
    { name: "Peptide Epsilon-5", sales: 128, revenue: 16640, growth: 5.9 },
];

const customerSegmentData = [
    { name: "B2C", value: 65, revenue: 45200, color: "#0088FE" },
    { name: "B2B", value: 25, revenue: 32800, color: "#00C49F" },
    { name: "Enterprise", value: 10, revenue: 28600, color: "#FFBB28" },
];

const geographicalData = [
    { region: "North America", orders: 456, revenue: 68400, percentage: 45.2 },
    { region: "Europe", orders: 234, revenue: 35100, percentage: 23.1 },
    { region: "Asia Pacific", orders: 189, revenue: 28350, percentage: 18.7 },
    { region: "South America", orders: 78, revenue: 11700, percentage: 7.7 },
    { region: "Others", orders: 43, revenue: 6450, percentage: 5.3 },
];

const conversionData = [
    { stage: "Visitors", count: 12850, percentage: 100 },
    { stage: "Product Views", count: 8945, percentage: 69.6 },
    { stage: "Add to Cart", count: 3421, percentage: 26.6 },
    { stage: "Checkout", count: 1876, percentage: 14.6 },
    { stage: "Purchase", count: 1245, percentage: 9.7 },
];

export function AnalyticsContent() {
    const [dateRange, setDateRange] = useState("last_30_days");
    const [activeTab, setActiveTab] = useState("overview");

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
                    <p className="text-muted-foreground">
                        Track your store performance and business insights
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Select value={dateRange} onValueChange={setDateRange}>
                        <SelectTrigger className="w-40">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="last_7_days">Last 7 days</SelectItem>
                            <SelectItem value="last_30_days">Last 30 days</SelectItem>
                            <SelectItem value="last_90_days">Last 90 days</SelectItem>
                            <SelectItem value="last_year">Last year</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                    </Button>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">$45,231.89</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +20.1% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Orders</CardTitle>
                        <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">1,235</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +15.2% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                        <Target className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">9.7%</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                            -2.1% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Avg. Order Value</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">$366.22</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +4.3% from last month
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Analytics Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="sales">Sales</TabsTrigger>
                    <TabsTrigger value="products">Products</TabsTrigger>
                    <TabsTrigger value="customers">Customers</TabsTrigger>
                    <TabsTrigger value="geography">Geography</TabsTrigger>
                </TabsList>

                {/* Overview Tab */}
                <TabsContent value="overview" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        {/* Revenue Chart */}
                        <Card className="col-span-1">
                            <CardHeader>
                                <CardTitle>Revenue Trend</CardTitle>
                                <CardDescription>Monthly revenue over time</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <ResponsiveContainer width="100%" height={300}>
                                    <AreaChart data={salesData}>
                                        <CartesianGrid strokeDasharray="3 3" />
                                        <XAxis dataKey="month" />
                                        <YAxis />
                                        <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, "Revenue"]} />
                                        <Area
                                            type="monotone"
                                            dataKey="revenue"
                                            stroke="hsl(var(--primary))"
                                            fill="hsl(var(--primary))"
                                            fillOpacity={0.2}
                                        />
                                    </AreaChart>
                                </ResponsiveContainer>
                            </CardContent>
                        </Card>

                        {/* Customer Segments */}
                        <Card className="col-span-1">
                            <CardHeader>
                                <CardTitle>Customer Segments</CardTitle>
                                <CardDescription>Revenue by customer type</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <ResponsiveContainer width="100%" height={300}>
                                    <PieChart>
                                        <Pie
                                            data={customerSegmentData}
                                            cx="50%"
                                            cy="50%"
                                            innerRadius={60}
                                            outerRadius={100}
                                            dataKey="revenue"
                                        >
                                            {customerSegmentData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.color} />
                                            ))}
                                        </Pie>
                                        <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, "Revenue"]} />
                                    </PieChart>
                                </ResponsiveContainer>
                                <div className="flex flex-col gap-2 mt-4">
                                    {customerSegmentData.map((item) => (
                                        <div key={item.name} className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{ backgroundColor: item.color }}
                                                />
                                                <span className="text-sm">{item.name}</span>
                                            </div>
                                            <span className="text-sm font-medium">${item.revenue.toLocaleString()}</span>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Conversion Funnel */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Conversion Funnel</CardTitle>
                            <CardDescription>Customer journey from visit to purchase</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {conversionData.map((stage, index) => (
                                    <div key={stage.stage} className="flex items-center gap-4">
                                        <div className="w-32 text-sm font-medium">{stage.stage}</div>
                                        <div className="flex-1">
                                            <div className="flex items-center justify-between mb-1">
                                                <span className="text-sm">{stage.count.toLocaleString()}</span>
                                                <span className="text-sm text-muted-foreground">{stage.percentage}%</span>
                                            </div>
                                            <div className="w-full bg-secondary rounded-full h-2">
                                                <div
                                                    className="bg-primary h-2 rounded-full transition-all duration-500"
                                                    style={{ width: `${stage.percentage}%` }}
                                                />
                                            </div>
                                        </div>
                                        {index < conversionData.length - 1 && (
                                            <div className="text-xs text-muted-foreground">
                                                -{((conversionData[index].count - conversionData[index + 1].count) / conversionData[index].count * 100).toFixed(1)}%
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Sales Tab */}
                <TabsContent value="sales" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Daily Sales</CardTitle>
                                <CardDescription>Revenue and orders by day of week</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <ResponsiveContainer width="100%" height={300}>
                                    <BarChart data={dailySalesData}>
                                        <CartesianGrid strokeDasharray="3 3" />
                                        <XAxis dataKey="day" />
                                        <YAxis />
                                        <Tooltip />
                                        <Bar dataKey="revenue" fill="hsl(var(--primary))" />
                                    </BarChart>
                                </ResponsiveContainer>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Sales Trends</CardTitle>
                                <CardDescription>Monthly comparison</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <ResponsiveContainer width="100%" height={300}>
                                    <LineChart data={salesData}>
                                        <CartesianGrid strokeDasharray="3 3" />
                                        <XAxis dataKey="month" />
                                        <YAxis />
                                        <Tooltip />
                                        <Line
                                            type="monotone"
                                            dataKey="revenue"
                                            stroke="hsl(var(--primary))"
                                            strokeWidth={2}
                                        />
                                        <Line
                                            type="monotone"
                                            dataKey="orders"
                                            stroke="hsl(var(--secondary))"
                                            strokeWidth={2}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* Products Tab */}
                <TabsContent value="products" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Top Products</CardTitle>
                            <CardDescription>Best performing products by sales and revenue</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Product</TableHead>
                                        <TableHead>Sales</TableHead>
                                        <TableHead>Revenue</TableHead>
                                        <TableHead>Growth</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {topProductsData.map((product, index) => (
                                        <TableRow key={product.name}>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">
                                                        {index + 1}
                                                    </div>
                                                    {product.name}
                                                </div>
                                            </TableCell>
                                            <TableCell>{product.sales}</TableCell>
                                            <TableCell>${product.revenue.toLocaleString()}</TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-1">
                                                    {product.growth > 0 ? (
                                                        <TrendingUp className="h-3 w-3 text-green-500" />
                                                    ) : (
                                                        <TrendingDown className="h-3 w-3 text-red-500" />
                                                    )}
                                                    <span className={product.growth > 0 ? "text-green-600" : "text-red-600"}>
                                                        {product.growth > 0 ? "+" : ""}{product.growth}%
                                                    </span>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Customers Tab */}
                <TabsContent value="customers" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-3">
                        <Card>
                            <CardHeader>
                                <CardTitle>Customer Acquisition</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <ResponsiveContainer width="100%" height={200}>
                                    <LineChart data={salesData}>
                                        <XAxis dataKey="month" />
                                        <YAxis />
                                        <Tooltip />
                                        <Line
                                            type="monotone"
                                            dataKey="customers"
                                            stroke="hsl(var(--primary))"
                                            strokeWidth={2}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Customer Lifetime Value</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-center">
                                    <div className="text-3xl font-bold">$2,450</div>
                                    <p className="text-sm text-muted-foreground">Average CLV</p>
                                    <div className="flex items-center justify-center gap-1 mt-2">
                                        <TrendingUp className="h-3 w-3 text-green-500" />
                                        <span className="text-sm text-green-600">+12.5%</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Customer Satisfaction</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-center">
                                    <div className="flex items-center justify-center gap-2">
                                        <Star className="h-6 w-6 fill-yellow-400 text-yellow-400" />
                                        <span className="text-3xl font-bold">4.8</span>
                                    </div>
                                    <p className="text-sm text-muted-foreground">Average Rating</p>
                                    <p className="text-xs text-muted-foreground mt-1">Based on 234 reviews</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* Geography Tab */}
                <TabsContent value="geography" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Sales by Region</CardTitle>
                            <CardDescription>Geographic distribution of orders and revenue</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Region</TableHead>
                                        <TableHead>Orders</TableHead>
                                        <TableHead>Revenue</TableHead>
                                        <TableHead>Share</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {geographicalData.map((region) => (
                                        <TableRow key={region.region}>
                                            <TableCell className="font-medium">{region.region}</TableCell>
                                            <TableCell>{region.orders}</TableCell>
                                            <TableCell>${region.revenue.toLocaleString()}</TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <div className="w-12 h-2 bg-secondary rounded-full overflow-hidden">
                                                        <div
                                                            className="h-full bg-primary rounded-full transition-all duration-500"
                                                            style={{ width: `${region.percentage}%` }}
                                                        />
                                                    </div>
                                                    <span className="text-sm">{region.percentage}%</span>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
}
