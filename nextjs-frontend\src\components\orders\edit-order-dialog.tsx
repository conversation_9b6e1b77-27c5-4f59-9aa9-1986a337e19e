'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Package, CreditCard, FileText, Truck } from 'lucide-react';
import { api, Order } from '@/lib/api';
import { toast } from 'sonner';
import { Dialog as UIDialog, DialogContent as UIDialogContent, DialogHeader as UIDialogHeader, DialogTitle as UIDialogTitle } from '@/components/ui/dialog';

interface EditOrderDialogProps {
  order: Order | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function EditOrderDialog({ order, open, onOpenChange, onSuccess }: EditOrderDialogProps) {
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState('');
  const [discountAmount, setDiscountAmount] = useState(0);
  const [shippingAmount, setShippingAmount] = useState(0);
  const [taxAmount, setTaxAmount] = useState(0);
  const [notes, setNotes] = useState('');
  const [showRefundDialog, setShowRefundDialog] = useState(false);
  const [refundAmount, setRefundAmount] = useState(0);
  const [refundReason, setRefundReason] = useState('');
  const [refundLoading, setRefundLoading] = useState(false);
  const [refundError, setRefundError] = useState('');
  const [refundStatusLoading, setRefundStatusLoading] = useState<string | null>(null);

  // Shipping state
  const [shipments, setShipments] = useState<any[]>([]);
  const [shipmentsLoading, setShipmentsLoading] = useState(false);
  const [showCreateShipment, setShowCreateShipment] = useState(false);
  const [newShipment, setNewShipment] = useState({
    carrier: '',
    trackingNumber: '',
    trackingUrl: '',
    status: 'PENDING'
  });

  useEffect(() => {
    if (order) {
      setStatus(order.status);
      setDiscountAmount(order.discountAmount);
      setShippingAmount(order.shippingAmount);
      setTaxAmount(order.taxAmount);
      setNotes('');
      fetchShipments();
    }
  }, [order]);

  const fetchShipments = async () => {
    if (!order) return;

    setShipmentsLoading(true);
    try {
      const response = await api.getOrderShipments(order.id);
      if (response.success) {
        setShipments(response.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch shipments:', error);
    } finally {
      setShipmentsLoading(false);
    }
  };

  const handleCreateShipment = async () => {
    if (!order) return;

    try {
      const response = await api.createShipment({
        orderId: order.id,
        ...newShipment
      });

      if (response.success) {
        toast.success('Shipment created successfully');
        setShowCreateShipment(false);
        setNewShipment({
          carrier: '',
          trackingNumber: '',
          trackingUrl: '',
          status: 'PENDING'
        });
        fetchShipments();
        onSuccess();
      } else {
        toast.error('Failed to create shipment');
      }
    } catch (error) {
      toast.error('Failed to create shipment');
    }
  };

  const handleUpdateShipment = async (shipmentId: string, data: any) => {
    try {
      const response = await api.updateShipmentTracking(shipmentId, data);

      if (response.success) {
        toast.success('Shipment updated successfully');
        fetchShipments();
        onSuccess();
      } else {
        toast.error('Failed to update shipment');
      }
    } catch (error) {
      toast.error('Failed to update shipment');
    }
  };

  const handleDeleteShipment = async (shipmentId: string) => {
    try {
      const response = await api.deleteShipment(shipmentId);

      if (response.success) {
        toast.success('Shipment deleted successfully');
        fetchShipments();
        onSuccess();
      } else {
        toast.error('Failed to delete shipment');
      }
    } catch (error) {
      toast.error('Failed to delete shipment');
    }
  };

  const handleSubmit = async () => {
    if (!order) return;

    try {
      setLoading(true);
      
      const updateData = {
        status,
        discountAmount: discountAmount.toString(),
        shippingAmount: shippingAmount.toString(),
        taxAmount: taxAmount.toString(),
      };

      const response = await api.updateOrder(order.id, updateData);
      
      if (response.success) {
        // Add note if provided
        if (notes.trim()) {
          await api.addOrderNote(order.id, notes.trim());
        }
        
        onSuccess();
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Failed to update order:', error);
      toast.error('Failed to update order');
    } finally {
      setLoading(false);
    }
  };

  const handleInitiateRefund = async () => {
    if (!order || !order.payments || order.payments.length === 0) return;
    setRefundLoading(true);
    setRefundError('');
    try {
      const payment = order.payments[0];
      const response = await api.initiateOrderRefund(order.id, refundAmount, refundReason);
      if (response.success) {
        setShowRefundDialog(false);
        setRefundAmount(0);
        setRefundReason('');
        onSuccess();
        toast.success('Refund initiated');
      } else {
        setRefundError(response.error || 'Failed to initiate refund');
      }
    } catch (e) {
      setRefundError('Failed to initiate refund');
    } finally {
      setRefundLoading(false);
    }
  };

  const handleUpdateRefundStatus = async (refundId: string, newStatus: string) => {
    setRefundStatusLoading(refundId);
    try {
      const response = await api.updateRefundStatus(refundId, newStatus);
      if (response.success) {
        onSuccess();
        toast.success('Refund status updated');
      } else {
        toast.error(response.error || 'Failed to update refund status');
      }
    } catch (e) {
      toast.error('Failed to update refund status');
    } finally {
      setRefundStatusLoading(null);
    }
  };

  if (!order) return null;

  const subtotal = order.subtotal || 0;
  const totalAmount = Number(subtotal ?? 0) - Number(discountAmount ?? 0) + Number(shippingAmount ?? 0) + Number(taxAmount ?? 0);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Order #{order.orderNumber}</DialogTitle>
          <DialogDescription>
            Update order details and status
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="details">Order Details</TabsTrigger>
            <TabsTrigger value="items">Items</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
            <TabsTrigger value="shipping">Shipping</TabsTrigger>
            <TabsTrigger value="audit">Audit Trail</TabsTrigger>
            <TabsTrigger value="refunds">Refunds</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Order Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="status">Order Status</Label>
                    <Select value={status} onValueChange={setStatus}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PENDING">Pending</SelectItem>
                        <SelectItem value="PROCESSING">Processing</SelectItem>
                        <SelectItem value="SHIPPED">Shipped</SelectItem>
                        <SelectItem value="DELIVERED">Delivered</SelectItem>
                        <SelectItem value="CANCELLED">Cancelled</SelectItem>
                        <SelectItem value="REFUNDED">Refunded</SelectItem>
                        <SelectItem value="ON_HOLD">On Hold</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Customer</Label>
                    <div className="p-2 bg-gray-50 rounded text-black">
                      {order.customer ? 
                        `${order.customer.firstName} ${order.customer.lastName}` : 
                        'Guest Customer'
                      }
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Order Summary</Label>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>${Number(subtotal ?? 0).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Discount:</span>
                      <Input
                        type="number"
                        value={discountAmount}
                        onChange={(e) => setDiscountAmount(parseFloat(e.target.value) || 0)}
                        className="w-24 text-right"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Shipping:</span>
                      <Input
                        type="number"
                        value={shippingAmount}
                        onChange={(e) => setShippingAmount(parseFloat(e.target.value) || 0)}
                        className="w-24 text-right"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Tax:</span>
                      <Input
                        type="number"
                        value={taxAmount}
                        onChange={(e) => setTaxAmount(parseFloat(e.target.value) || 0)}
                        className="w-24 text-right"
                        min="0"
                        step="0.01"
                      />
                    </div>
                    <div className="flex justify-between font-medium text-lg pt-2 border-t">
                      <span>Total:</span>
                      <span>${Number(totalAmount ?? 0).toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Add Note (Optional)</Label>
                  <Textarea
                    id="notes"
                    placeholder="Add a note about this order update..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="items" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="w-5 h-5" />
                  Order Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {order.items && order.items.length > 0 ? (
                    order.items.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex-1">
                          <div className="font-medium">
                            {item.variant?.product?.name || 'Unknown Product'} - {item.variant?.name || 'Unknown Variant'}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            SKU: {item.variant?.sku || 'N/A'}
                            {order.customer?.customerType && item.variant?.segmentPrices && item.variant.segmentPrices.length > 0 && (
                              <span className="ml-2">
                                (Customer Segment: {order.customer.customerType})
                              </span>
                            )}
                          </div>
                          {item.variant && item.variant.segmentPrices && item.variant.segmentPrices.length > 0 && (
                            <div className="text-xs text-muted-foreground mt-1">
                              Segment Prices:
                              {item.variant.segmentPrices.map(sp => (
                                <span key={sp.id} className="ml-2">
                                  {sp.customerType}: ${sp.salePrice || sp.regularPrice}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-4">
                          <span className="text-sm">Qty: {item.quantity}</span>
                          <span className="text-sm">Unit: ${Number(item.unitPrice).toFixed(2)}</span>
                          <span className="text-sm font-medium">${Number(item.totalPrice).toFixed(2)}</span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted-foreground text-center py-4">No items found</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payments" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Payment History
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {order.payments && order.payments.length > 0 ? (
                    order.payments.map((payment) => (
                      <div key={payment.id} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex-1">
                          <div className="font-medium">
                            {payment.paymentMethod}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {payment.paidAt ? new Date(payment.paidAt).toLocaleDateString() : 'Not paid'}
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge variant={payment.status === 'PAID' ? 'default' : 'secondary'}>
                            {payment.status}
                          </Badge>
                          <span className="text-sm font-medium">${Number(payment.amount ?? 0).toFixed(2)}</span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted-foreground text-center py-4">No payments found</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="shipping" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="w-5 h-5" />
                  Shipping Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <Label>Billing Address</Label>
                      <div className="p-2 bg-gray-50 rounded text-sm text-black">
                        {order.billingAddress ? (
                          <div>
                            <div>{order.billingAddress.firstName} {order.billingAddress.lastName}</div>
                            <div>{order.billingAddress.address1}</div>
                            {order.billingAddress.address2 && <div>{order.billingAddress.address2}</div>}
                            <div>{order.billingAddress.city}, {order.billingAddress.state} {order.billingAddress.postalCode}</div>
                            <div>{order.billingAddress.country}</div>
                          </div>
                        ) : (
                          'No billing address'
                        )}
                      </div>
                    </div>

                    <div>
                      <Label>Shipping Address</Label>
                      <div className="p-2 bg-gray-50 rounded text-sm text-black">
                        {order.shippingAddress ? (
                          <div>
                            <div>{order.shippingAddress.firstName} {order.shippingAddress.lastName}</div>
                            <div>{order.shippingAddress.address1}</div>
                            {order.shippingAddress.address2 && <div>{order.shippingAddress.address2}</div>}
                            <div>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}</div>
                            <div>{order.shippingAddress.country}</div>
                          </div>
                        ) : (
                          'No shipping address'
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label>Shipments</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowCreateShipment(true)}
                      >
                        Create Shipment
                      </Button>
                    </div>

                    {shipmentsLoading ? (
                      <div className="text-center py-4 text-muted-foreground">
                        Loading shipments...
                      </div>
                    ) : shipments.length > 0 ? (
                      shipments.map((shipment) => (
                        <div key={shipment.id} className="p-4 border rounded space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-medium">{shipment.carrier}</div>
                              <div className="text-sm text-muted-foreground">
                                Tracking: {shipment.trackingNumber || 'N/A'}
                              </div>
                              {shipment.trackingUrl && (
                                <a
                                  href={shipment.trackingUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-sm text-blue-600 hover:underline"
                                >
                                  Track Package
                                </a>
                              )}
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant={shipment.status === 'DELIVERED' ? 'default' : 'outline'}>
                                {shipment.status}
                              </Badge>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteShipment(shipment.id)}
                              >
                                Delete
                              </Button>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label className="text-xs">Tracking Number</Label>
                              <Input
                                value={shipment.trackingNumber || ''}
                                onChange={(e) => handleUpdateShipment(shipment.id, { trackingNumber: e.target.value })}
                                placeholder="Enter tracking number"
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label className="text-xs">Tracking URL</Label>
                              <Input
                                value={shipment.trackingUrl || ''}
                                onChange={(e) => handleUpdateShipment(shipment.id, { trackingUrl: e.target.value })}
                                placeholder="Enter tracking URL"
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label className="text-xs">Status</Label>
                              <Select
                                value={shipment.status}
                                onValueChange={(value) => handleUpdateShipment(shipment.id, { status: value })}
                              >
                                <SelectTrigger className="mt-1">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="PENDING">Pending</SelectItem>
                                  <SelectItem value="SHIPPED">Shipped</SelectItem>
                                  <SelectItem value="IN_TRANSIT">In Transit</SelectItem>
                                  <SelectItem value="DELIVERED">Delivered</SelectItem>
                                  <SelectItem value="RETURNED">Returned</SelectItem>
                                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label className="text-xs">Carrier</Label>
                              <Input
                                value={shipment.carrier}
                                onChange={(e) => handleUpdateShipment(shipment.id, { carrier: e.target.value })}
                                placeholder="Enter carrier"
                                className="mt-1"
                              />
                            </div>
                          </div>

                          <div className="text-xs text-muted-foreground">
                            Created: {new Date(shipment.createdAt).toLocaleString()}
                            {shipment.shippedAt && (
                              <> • Shipped: {new Date(shipment.shippedAt).toLocaleString()}</>
                            )}
                            {shipment.deliveredAt && (
                              <> • Delivered: {new Date(shipment.deliveredAt).toLocaleString()}</>
                            )}
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-muted-foreground text-center py-4">No shipments found</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="audit" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Audit Trail
                </CardTitle>
              </CardHeader>
              <CardContent>
                {order.auditLogs && order.auditLogs.length > 0 ? (
                  <div className="space-y-3">
                    {order.auditLogs.map((log) => (
                      <div key={log.id} className="flex items-center gap-4 p-3 border rounded">
                        <div className="flex-1">
                          <div className="font-medium">
                            {log.action.replace(/_/g, ' ')}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {log.user ? `${log.user.firstName} ${log.user.lastName} (${log.user.email})` : 'System'}
                            {' • '}
                            {new Date(log.createdAt).toLocaleString()}
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            {log.details && typeof log.details === 'object' ? (
                              <pre className="whitespace-pre-wrap break-all bg-gray-50 p-2 rounded text-xs">{JSON.stringify(log.details, null, 2)}</pre>
                            ) : (
                              <span>{log.details}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-4">No audit logs found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="refunds" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Refunds
                </CardTitle>
              </CardHeader>
              <CardContent>
                {order.payments && order.payments.some(p => p.refunds && p.refunds.length > 0) ? (
                  <div className="space-y-3">
                    {order.payments.map(payment => (
                      payment.refunds && payment.refunds.length > 0 && payment.refunds.map(refund => (
                        <div key={refund.id} className="flex items-center gap-4 p-3 border rounded">
                          <div className="flex-1">
                            <div className="font-medium">
                              Refund: ${Number(refund.amount).toFixed(2)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Status: {refund.status}
                              {refund.refundedAt && (
                                <>
                                  {' • '}Refunded: {new Date(refund.refundedAt).toLocaleString()}
                                </>
                              )}
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {refund.reason}
                            </div>
                          </div>
                          {/* Admin: allow status update */}
                          <div className="flex flex-col gap-2">
                            {['PENDING', 'APPROVED'].includes(refund.status) && (
                              <>
                                <Button size="sm" variant="outline" disabled={refundStatusLoading === refund.id} onClick={() => handleUpdateRefundStatus(refund.id, 'APPROVED')}>Approve</Button>
                                <Button size="sm" variant="outline" disabled={refundStatusLoading === refund.id} onClick={() => handleUpdateRefundStatus(refund.id, 'PROCESSED')}>Mark as Processed</Button>
                                <Button size="sm" variant="destructive" disabled={refundStatusLoading === refund.id} onClick={() => handleUpdateRefundStatus(refund.id, 'REJECTED')}>Reject</Button>
                              </>
                            )}
                          </div>
                        </div>
                      ))
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-4">No refunds found</p>
                )}
                {/* Admin: Refund button */}
                <div className="mt-4">
                  <Button variant="outline" onClick={() => setShowRefundDialog(true)}>Initiate Refund</Button>
                </div>
                <UIDialog open={showRefundDialog} onOpenChange={setShowRefundDialog}>
                  <UIDialogContent>
                    <UIDialogHeader>
                      <UIDialogTitle>Initiate Refund</UIDialogTitle>
                    </UIDialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label>Amount</Label>
                        <Input type="number" min={0} max={order.totalAmount} value={refundAmount} onChange={e => setRefundAmount(Number(e.target.value))} />
                      </div>
                      <div>
                        <Label>Reason</Label>
                        <Textarea value={refundReason} onChange={e => setRefundReason(e.target.value)} />
                      </div>
                      {refundError && <div className="text-red-600 text-sm">{refundError}</div>}
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setShowRefundDialog(false)}>Cancel</Button>
                        <Button onClick={handleInitiateRefund} disabled={refundLoading || refundAmount <= 0}>{refundLoading ? 'Processing...' : 'Submit Refund'}</Button>
                      </div>
                    </div>
                  </UIDialogContent>
                </UIDialog>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Updating...' : 'Update Order'}
          </Button>
        </div>
      </DialogContent>

      {/* Create Shipment Dialog */}
      <UIDialog open={showCreateShipment} onOpenChange={setShowCreateShipment}>
        <UIDialogContent>
          <UIDialogHeader>
            <UIDialogTitle>Create Shipment</UIDialogTitle>
          </UIDialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Carrier *</Label>
              <Input
                value={newShipment.carrier}
                onChange={(e) => setNewShipment({ ...newShipment, carrier: e.target.value })}
                placeholder="e.g., FedEx, UPS, DHL"
                required
              />
            </div>
            <div>
              <Label>Tracking Number</Label>
              <Input
                value={newShipment.trackingNumber}
                onChange={(e) => setNewShipment({ ...newShipment, trackingNumber: e.target.value })}
                placeholder="Enter tracking number"
              />
            </div>
            <div>
              <Label>Tracking URL</Label>
              <Input
                value={newShipment.trackingUrl}
                onChange={(e) => setNewShipment({ ...newShipment, trackingUrl: e.target.value })}
                placeholder="Enter tracking URL"
              />
            </div>
            <div>
              <Label>Status</Label>
              <Select
                value={newShipment.status}
                onValueChange={(value) => setNewShipment({ ...newShipment, status: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="SHIPPED">Shipped</SelectItem>
                  <SelectItem value="IN_TRANSIT">In Transit</SelectItem>
                  <SelectItem value="DELIVERED">Delivered</SelectItem>
                  <SelectItem value="RETURNED">Returned</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowCreateShipment(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateShipment} disabled={!newShipment.carrier}>
                Create Shipment
              </Button>
            </div>
          </div>
        </UIDialogContent>
      </UIDialog>
    </Dialog>
  );
}