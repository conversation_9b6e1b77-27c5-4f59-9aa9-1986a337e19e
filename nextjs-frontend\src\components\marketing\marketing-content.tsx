"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Progress } from "@/components/ui/progress";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CartesianGrid,
    <PERSON>ltip,
    ResponsiveContainer,
    LineChart,
    Line,
    PieChart,
    Pie,
    Cell
} from "recharts";
import {
    MessageSquare,
    Mail,
    Gift,
    Users,
    TrendingUp,
    Calendar,
    Target,
    Send,
    Eye,
    MousePointerClick,
    DollarSign,
    MoreHorizontal,
    Plus,
    Edit,
    Trash2,
    Play,
    Pause,
    Copy
} from "lucide-react";

// Mock data
const campaignData = [
    { month: "Jan", emailOpen: 65, smsOpen: 78, revenue: 12400 },
    { month: "Feb", emailOpen: 72, smsOpen: 81, revenue: 15600 },
    { month: "Mar", emailOpen: 68, smsOpen: 75, revenue: 18900 },
    { month: "Apr", emailOpen: 75, smsOpen: 84, revenue: 16200 },
    { month: "May", emailOpen: 82, smsOpen: 88, revenue: 21500 },
    { month: "Jun", emailOpen: 79, smsOpen: 86, revenue: 19800 },
];

const channelData = [
    { name: "Email", value: 45, color: "#0088FE" },
    { name: "SMS", value: 25, color: "#00C49F" },
    { name: "Push", value: 20, color: "#FFBB28" },
    { name: "Social", value: 10, color: "#FF8042" },
];

const campaigns = [
    {
        id: 1,
        name: "Summer Peptide Sale",
        type: "Email",
        status: "Active",
        audience: 1250,
        sent: 1200,
        opens: 456,
        clicks: 89,
        revenue: 12400,
        createdAt: "2024-06-15",
    },
    {
        id: 2,
        name: "New Customer Welcome",
        type: "Automation",
        status: "Active",
        audience: 890,
        sent: 856,
        opens: 523,
        clicks: 156,
        revenue: 8900,
        createdAt: "2024-06-10",
    },
    {
        id: 3,
        name: "Research Product Launch",
        type: "SMS",
        status: "Completed",
        audience: 2100,
        sent: 2100,
        opens: 1847,
        clicks: 234,
        revenue: 15600,
        createdAt: "2024-06-08",
    },
    {
        id: 4,
        name: "Loyalty Program Invite",
        type: "Email",
        status: "Draft",
        audience: 3400,
        sent: 0,
        opens: 0,
        clicks: 0,
        revenue: 0,
        createdAt: "2024-06-12",
    },
];

const loyaltyMembers = [
    {
        id: 1,
        name: "Dr. Sarah Wilson",
        email: "<EMAIL>",
        tier: "Platinum",
        points: 2450,
        totalSpent: 15600,
        joinDate: "2024-01-15",
    },
    {
        id: 2,
        name: "Prof. Michael Chen",
        email: "<EMAIL>",
        tier: "Gold",
        points: 1890,
        totalSpent: 12300,
        joinDate: "2024-02-20",
    },
    {
        id: 3,
        name: "Dr. Emily Rodriguez",
        email: "<EMAIL>",
        tier: "Silver",
        points: 1240,
        totalSpent: 8900,
        joinDate: "2024-03-10",
    },
    {
        id: 4,
        name: "Dr. James Parker",
        email: "<EMAIL>",
        tier: "Bronze",
        points: 650,
        totalSpent: 4500,
        joinDate: "2024-04-05",
    },
];

const StatusBadge = ({ status }: { status: string }) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
        Active: "default",
        Completed: "secondary",
        Draft: "outline",
        Paused: "destructive",
    };

    return <Badge variant={variants[status]}>{status}</Badge>;
};

const TierBadge = ({ tier }: { tier: string }) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
        Platinum: "default",
        Gold: "secondary",
        Silver: "outline",
        Bronze: "destructive",
    };

    return <Badge variant={variants[tier]}>{tier}</Badge>;
};

export function MarketingContent() {
    const [selectedCampaign, setSelectedCampaign] = useState<number | null>(null);

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Marketing</h1>
                    <p className="text-muted-foreground">
                        Manage your marketing campaigns, promotions, and customer engagement.
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline">
                        <Calendar className="h-4 w-4 mr-2" />
                        Campaign Calendar
                    </Button>
                    <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Campaign
                    </Button>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
                        <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">12</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +2 from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Reach</CardTitle>
                        <Eye className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">24,567</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +15.2% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Click-through Rate</CardTitle>
                        <MousePointerClick className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">4.8%</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +0.3% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Marketing Revenue</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">$68,421</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +22.1% from last month
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Tabs */}
            <Tabs defaultValue="campaigns" className="space-y-6">
                <TabsList>
                    <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
                    <TabsTrigger value="analytics">Analytics</TabsTrigger>
                    <TabsTrigger value="loyalty">Loyalty Program</TabsTrigger>
                    <TabsTrigger value="automation">Automation</TabsTrigger>
                </TabsList>

                {/* Campaigns Tab */}
                <TabsContent value="campaigns" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-3">
                        {/* Campaign List */}
                        <Card className="md:col-span-2">
                            <CardHeader>
                                <CardTitle>Marketing Campaigns</CardTitle>
                                <CardDescription>
                                    Manage your email, SMS, and social media campaigns
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Campaign</TableHead>
                                            <TableHead>Type</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead>Audience</TableHead>
                                            <TableHead>Performance</TableHead>
                                            <TableHead>Revenue</TableHead>
                                            <TableHead></TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {campaigns.map((campaign) => (
                                            <TableRow key={campaign.id}>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{campaign.name}</div>
                                                        <div className="text-sm text-muted-foreground">
                                                            Created {campaign.createdAt}
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="outline">{campaign.type}</Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <StatusBadge status={campaign.status} />
                                                </TableCell>
                                                <TableCell>{campaign.audience.toLocaleString()}</TableCell>
                                                <TableCell>
                                                    <div className="text-sm">
                                                        <div>Opens: {campaign.opens}</div>
                                                        <div>Clicks: {campaign.clicks}</div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>${campaign.revenue.toLocaleString()}</TableCell>
                                                <TableCell>
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                                <MoreHorizontal className="h-4 w-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuItem>
                                                                <Eye className="h-4 w-4 mr-2" />
                                                                View Details
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem>
                                                                <Edit className="h-4 w-4 mr-2" />
                                                                Edit Campaign
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem>
                                                                <Copy className="h-4 w-4 mr-2" />
                                                                Duplicate
                                                            </DropdownMenuItem>
                                                            <DropdownMenuSeparator />
                                                            <DropdownMenuItem>
                                                                {campaign.status === "Active" ? (
                                                                    <>
                                                                        <Pause className="h-4 w-4 mr-2" />
                                                                        Pause Campaign
                                                                    </>
                                                                ) : (
                                                                    <>
                                                                        <Play className="h-4 w-4 mr-2" />
                                                                        Activate Campaign
                                                                    </>
                                                                )}
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem className="text-red-600">
                                                                <Trash2 className="h-4 w-4 mr-2" />
                                                                Delete Campaign
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </CardContent>
                        </Card>

                        {/* Quick Actions */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Quick Actions</CardTitle>
                                <CardDescription>
                                    Common marketing tasks
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <Button className="w-full justify-start">
                                    <Mail className="h-4 w-4 mr-2" />
                                    Create Email Campaign
                                </Button>
                                <Button variant="outline" className="w-full justify-start">
                                    <MessageSquare className="h-4 w-4 mr-2" />
                                    Send SMS Campaign
                                </Button>
                                <Button variant="outline" className="w-full justify-start">
                                    <Gift className="h-4 w-4 mr-2" />
                                    Create Promotion
                                </Button>
                                <Button variant="outline" className="w-full justify-start">
                                    <Target className="h-4 w-4 mr-2" />
                                    Audience Segmentation
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* Analytics Tab */}
                <TabsContent value="analytics" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        {/* Campaign Performance */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Campaign Performance</CardTitle>
                                <CardDescription>
                                    Email and SMS campaign metrics over time
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <ResponsiveContainer width="100%" height={300}>
                                    <LineChart data={campaignData}>
                                        <CartesianGrid strokeDasharray="3 3" />
                                        <XAxis dataKey="month" />
                                        <YAxis />
                                        <Tooltip />
                                        <Line
                                            type="monotone"
                                            dataKey="emailOpen"
                                            stroke="#0088FE"
                                            strokeWidth={2}
                                            name="Email Open Rate"
                                        />
                                        <Line
                                            type="monotone"
                                            dataKey="smsOpen"
                                            stroke="#00C49F"
                                            strokeWidth={2}
                                            name="SMS Open Rate"
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </CardContent>
                        </Card>

                        {/* Channel Distribution */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Channel Distribution</CardTitle>
                                <CardDescription>
                                    Marketing reach by channel
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <ResponsiveContainer width="100%" height={300}>
                                    <PieChart>
                                        <Pie
                                            data={channelData}
                                            cx="50%"
                                            cy="50%"
                                            innerRadius={60}
                                            outerRadius={100}
                                            dataKey="value"
                                        >
                                            {channelData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.color} />
                                            ))}
                                        </Pie>
                                        <Tooltip />
                                    </PieChart>
                                </ResponsiveContainer>
                                <div className="flex flex-col gap-2 mt-4">
                                    {channelData.map((item) => (
                                        <div key={item.name} className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <div
                                                    className="w-3 h-3 rounded-full"
                                                    style={{ backgroundColor: item.color }}
                                                />
                                                <span className="text-sm">{item.name}</span>
                                            </div>
                                            <span className="text-sm font-medium">{item.value}%</span>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* Loyalty Program Tab */}
                <TabsContent value="loyalty" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-3">
                        {/* Program Stats */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Program Overview</CardTitle>
                                <CardDescription>
                                    Loyalty program statistics
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Total Members</span>
                                    <span className="text-2xl font-bold">1,247</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Active This Month</span>
                                    <span className="text-lg font-semibold">892</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Points Redeemed</span>
                                    <span className="text-lg font-semibold">45,230</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium">Average Spend</span>
                                    <span className="text-lg font-semibold">$342</span>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Top Members */}
                        <Card className="md:col-span-2">
                            <CardHeader>
                                <CardTitle>Top Loyalty Members</CardTitle>
                                <CardDescription>
                                    Highest value loyalty program members
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Member</TableHead>
                                            <TableHead>Tier</TableHead>
                                            <TableHead>Points</TableHead>
                                            <TableHead>Total Spent</TableHead>
                                            <TableHead>Join Date</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {loyaltyMembers.map((member) => (
                                            <TableRow key={member.id}>
                                                <TableCell>
                                                    <div>
                                                        <div className="font-medium">{member.name}</div>
                                                        <div className="text-sm text-muted-foreground">{member.email}</div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <TierBadge tier={member.tier} />
                                                </TableCell>
                                                <TableCell>{member.points.toLocaleString()}</TableCell>
                                                <TableCell>${member.totalSpent.toLocaleString()}</TableCell>
                                                <TableCell>{member.joinDate}</TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* Automation Tab */}
                <TabsContent value="automation" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        {/* Automation Rules */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Active Automations</CardTitle>
                                <CardDescription>
                                    Automated marketing workflows
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="flex items-center gap-3">
                                        <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                                        <div>
                                            <p className="font-medium">Welcome Series</p>
                                            <p className="text-sm text-muted-foreground">New customer onboarding</p>
                                        </div>
                                    </div>
                                    <Badge>Active</Badge>
                                </div>

                                <div className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="flex items-center gap-3">
                                        <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                                        <div>
                                            <p className="font-medium">Abandoned Cart</p>
                                            <p className="text-sm text-muted-foreground">Cart recovery emails</p>
                                        </div>
                                    </div>
                                    <Badge>Active</Badge>
                                </div>

                                <div className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="flex items-center gap-3">
                                        <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
                                        <div>
                                            <p className="font-medium">Win-back Campaign</p>
                                            <p className="text-sm text-muted-foreground">Re-engage inactive customers</p>
                                        </div>
                                    </div>
                                    <Badge variant="outline">Paused</Badge>
                                </div>

                                <div className="flex items-center justify-between p-4 border rounded-lg">
                                    <div className="flex items-center gap-3">
                                        <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                                        <div>
                                            <p className="font-medium">Birthday Rewards</p>
                                            <p className="text-sm text-muted-foreground">Birthday discount offers</p>
                                        </div>
                                    </div>
                                    <Badge>Active</Badge>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Create Automation */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Create New Automation</CardTitle>
                                <CardDescription>
                                    Set up automated marketing workflows
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="automation-name">Automation Name</Label>
                                    <Input id="automation-name" placeholder="Enter automation name" />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="trigger">Trigger</Label>
                                    <select className="w-full p-2 border rounded-md">
                                        <option>Select trigger event</option>
                                        <option>Customer signup</option>
                                        <option>First purchase</option>
                                        <option>Abandoned cart</option>
                                        <option>Birthday</option>
                                        <option>Subscription renewal</option>
                                    </select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="delay">Delay</Label>
                                    <div className="flex gap-2">
                                        <Input type="number" placeholder="1" className="w-20" />
                                        <select className="flex-1 p-2 border rounded-md">
                                            <option>Hours</option>
                                            <option>Days</option>
                                            <option>Weeks</option>
                                        </select>
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="message">Message Content</Label>
                                    <Textarea
                                        id="message"
                                        placeholder="Enter your message content..."
                                        rows={4}
                                    />
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch id="active" />
                                    <Label htmlFor="active">Activate immediately</Label>
                                </div>

                                <Button className="w-full">
                                    Create Automation
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
}
