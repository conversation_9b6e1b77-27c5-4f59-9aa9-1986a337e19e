"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { BatchExpiryAlerts } from "./batch-expiry-alerts";
import {
    BarChart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    LineChart,
    Line,
    PieChart,
    Pie,
    Cell,
    AreaChart,
    Area
} from "recharts";
import {
    TrendingUp,
    TrendingDown,
    DollarSign,
    ShoppingCart,
    Users,
    Package,
    Eye,
    MoreHorizontal,
    ArrowUpRight,
    ArrowDownRight
} from "lucide-react";

// Mock data
const salesData = [
    { month: "Jan", revenue: 12400, orders: 145 },
    { month: "Feb", revenue: 15600, orders: 178 },
    { month: "Mar", revenue: 18900, orders: 203 },
    { month: "Apr", revenue: 16200, orders: 187 },
    { month: "May", revenue: 21500, orders: 234 },
    { month: "Jun", revenue: 19800, orders: 218 },
];

const customerTypeData = [
    { name: "B2C", value: 65, color: "#0088FE" },
    { name: "B2B", value: 25, color: "#00C49F" },
    { name: "Enterprise", value: 10, color: "#FFBB28" },
];

const topProducts = [
    {
        id: 1,
        name: "Peptide Alpha-1",
        image: "/products/peptide-1.jpg",
        sales: 234,
        revenue: 12400,
        stock: 45,
        trend: "up"
    },
    {
        id: 2,
        name: "Peptide Beta-2",
        image: "/products/peptide-2.jpg",
        sales: 189,
        revenue: 9800,
        stock: 23,
        trend: "up"
    },
    {
        id: 3,
        name: "Peptide Gamma-3",
        image: "/products/peptide-3.jpg",
        sales: 156,
        revenue: 8200,
        stock: 12,
        trend: "down"
    },
    {
        id: 4,
        name: "Peptide Delta-4",
        image: "/products/peptide-4.jpg",
        sales: 142,
        revenue: 7600,
        stock: 67,
        trend: "up"
    },
];

const recentOrders = [
    {
        id: "#ORD-001",
        customer: "John Doe",
        email: "<EMAIL>",
        amount: 450.00,
        status: "completed",
        date: "2 min ago"
    },
    {
        id: "#ORD-002",
        customer: "Sarah Wilson",
        email: "<EMAIL>",
        amount: 1200.00,
        status: "processing",
        date: "15 min ago"
    },
    {
        id: "#ORD-003",
        customer: "Mike Johnson",
        email: "<EMAIL>",
        amount: 850.00,
        status: "shipped",
        date: "1 hour ago"
    },
    {
        id: "#ORD-004",
        customer: "Emily Chen",
        email: "<EMAIL>",
        amount: 2100.00,
        status: "pending",
        date: "2 hours ago"
    },
];

const StatusBadge = ({ status }: { status: string }) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
        completed: "default",
        processing: "secondary",
        shipped: "outline",
        pending: "destructive"
    };

    return <Badge variant={variants[status]}>{status}</Badge>;
};

export function DashboardContent() {
    return (
        <div className="space-y-6">
            {/* Welcome Section */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
                    <p className="text-muted-foreground">
                        Welcome back! Here&apos;s what&apos;s happening with your store today.
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline">
                        Download Report
                    </Button>
                    <Button>
                        <Eye className="h-4 w-4 mr-2" />
                        View Store
                    </Button>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">$45,231.89</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +20.1% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Orders</CardTitle>
                        <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">+2,350</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +15.2% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Customers</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">+12,234</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +19% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Active Products</CardTitle>
                        <Package className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">573</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                            -2.1% from last month
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Charts and Tables */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
                {/* Revenue Chart */}
                <Card className="col-span-4">
                    <CardHeader>
                        <CardTitle>Revenue Overview</CardTitle>
                        <CardDescription>
                            Monthly revenue and order trends
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                            <AreaChart data={salesData}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="month" />
                                <YAxis />
                                <Tooltip />
                                <Area
                                    type="monotone"
                                    dataKey="revenue"
                                    stroke="hsl(var(--primary))"
                                    fill="hsl(var(--primary))"
                                    fillOpacity={0.2}
                                />
                            </AreaChart>
                        </ResponsiveContainer>
                    </CardContent>
                </Card>

                {/* Customer Distribution */}
                <Card className="col-span-3">
                    <CardHeader>
                        <CardTitle>Customer Distribution</CardTitle>
                        <CardDescription>
                            Breakdown by customer type
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                                <Pie
                                    data={customerTypeData}
                                    cx="50%"
                                    cy="50%"
                                    innerRadius={60}
                                    outerRadius={100}
                                    dataKey="value"
                                >
                                    {customerTypeData.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={entry.color} />
                                    ))}
                                </Pie>
                                <Tooltip />
                            </PieChart>
                        </ResponsiveContainer>
                        <div className="flex flex-col gap-2 mt-4">
                            {customerTypeData.map((item) => (
                                <div key={item.name} className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <div
                                            className="w-3 h-3 rounded-full"
                                            style={{ backgroundColor: item.color }}
                                        />
                                        <span className="text-sm">{item.name}</span>
                                    </div>
                                    <span className="text-sm font-medium">{item.value}%</span>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Tables Section */}
            <div className="grid gap-6 md:grid-cols-2">
                {/* Top Products */}
                <Card>
                    <CardHeader>
                        <CardTitle>Top Products</CardTitle>
                        <CardDescription>
                            Best performing products this month
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {topProducts.map((product) => (
                                <div key={product.id} className="flex items-center gap-4">
                                    <Avatar className="h-12 w-12">
                                        <AvatarImage src={product.image} alt={product.name} />
                                        <AvatarFallback>{product.name.charAt(0)}</AvatarFallback>
                                    </Avatar>
                                    <div className="flex-1 space-y-1">
                                        <div className="flex items-center justify-between">
                                            <p className="text-sm font-medium">{product.name}</p>
                                            {product.trend === "up" ? (
                                                <ArrowUpRight className="h-4 w-4 text-green-500" />
                                            ) : (
                                                <ArrowDownRight className="h-4 w-4 text-red-500" />
                                            )}
                                        </div>
                                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                                            <span>{product.sales} sales</span>
                                            <span>${product.revenue.toLocaleString()}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="text-xs text-muted-foreground">Stock:</span>
                                            <div className="flex-1">
                                                <Progress value={(product.stock / 100) * 100} className="h-1" />
                                            </div>
                                            <span className="text-xs text-muted-foreground">{product.stock}</span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Recent Orders */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Orders</CardTitle>
                        <CardDescription>
                            Latest orders from your customers
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Order</TableHead>
                                    <TableHead>Customer</TableHead>
                                    <TableHead>Amount</TableHead>
                                    <TableHead>Status</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {recentOrders.map((order) => (
                                    <TableRow key={order.id}>
                                        <TableCell className="font-medium">{order.id}</TableCell>
                                        <TableCell>
                                            <div>
                                                <div className="font-medium">{order.customer}</div>
                                                <div className="text-sm text-muted-foreground">{order.email}</div>
                                            </div>
                                        </TableCell>
                                        <TableCell>${order.amount.toFixed(2)}</TableCell>
                                        <TableCell>
                                            <StatusBadge status={order.status} />
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
            </div>

            {/* Quick Actions and Alerts */}
            <div className="grid gap-6 md:grid-cols-2">
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                        <CardDescription>
                            Common tasks and shortcuts to help you manage your store
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 grid-cols-2">
                            <Button variant="outline" className="h-20 flex-col gap-2">
                                <Package className="h-6 w-6" />
                                Add Product
                            </Button>
                            <Button variant="outline" className="h-20 flex-col gap-2">
                                <Users className="h-6 w-6" />
                                Add Customer
                            </Button>
                            <Button variant="outline" className="h-20 flex-col gap-2">
                                <ShoppingCart className="h-6 w-6" />
                                Create Order
                            </Button>
                            <Button variant="outline" className="h-20 flex-col gap-2">
                                <MoreHorizontal className="h-6 w-6" />
                                View Reports
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Batch Expiry Alerts */}
                <BatchExpiryAlerts />
            </div>
        </div>
    );
}
