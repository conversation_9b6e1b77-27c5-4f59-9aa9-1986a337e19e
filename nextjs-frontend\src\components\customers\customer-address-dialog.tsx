'use client';

import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Plus, Edit, Trash2, MapPin, Home, Building } from 'lucide-react';
import { api, Customer, Address } from '@/lib/api';
import { toast } from 'sonner';
import { Country, State, City } from 'country-state-city';
import { PhoneInputWithFlag } from './phone-input-with-flag';

interface CustomerAddressDialogProps {
  customer: Customer | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface AddressFormData {
  type: 'BILLING' | 'SHIPPING';
  firstName: string;
  lastName: string;
  company: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone: string;
  isDefault: boolean;
}

export function CustomerAddressDialog({ customer, open, onOpenChange, onSuccess }: CustomerAddressDialogProps) {
  const [loading, setLoading] = useState(false);
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [formData, setFormData] = useState<AddressFormData>({
    type: 'BILLING',
    firstName: '',
    lastName: '',
    company: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    postalCode: '',
    country: '',
    phone: '',
    isDefault: false,
  });

  // Country-State-City data
  const [selectedCountry, setSelectedCountry] = useState('US');
  const [selectedState, setSelectedState] = useState('');
  const [selectedCity, setSelectedCity] = useState('');

  // Get data from country-state-city package
  const countries = Country.getAllCountries();
  const states = selectedCountry ? State.getStatesOfCountry(selectedCountry) : [];
  const cities = selectedCountry && selectedState ? City.getCitiesOfState(selectedCountry, selectedState) : [];

  // Initialize with US as default when dialog opens
  useEffect(() => {
    if (open && !editingAddress) {
      resetForm();
    }
  }, [open, editingAddress]);

  useEffect(() => {
    if (customer && open) {
      fetchAddresses();
    }
  }, [customer, open]);

  const fetchAddresses = async () => {
    if (!customer) return;
    
    try {
      const response = await api.getCustomer(customer.id);
      if (response.success && response.data) {
        setAddresses(response.data.addresses || []);
      }
    } catch (error) {
      console.error('Failed to fetch addresses:', error);
      toast.error('Failed to load addresses');
    }
  };

  const resetForm = () => {
    setFormData({
      type: 'BILLING',
      firstName: customer?.firstName || '',
      lastName: customer?.lastName || '',
      company: '',
      address1: '',
      address2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'United States',
      phone: customer?.mobile || '',
      isDefault: false,
    });
    setSelectedCountry('US');
    setSelectedState('');
    setSelectedCity('');
  };

  const handleAddAddress = () => {
    resetForm();
    setShowAddForm(true);
    setEditingAddress(null);
  };

  const handleEditAddress = (address: Address) => {
    setFormData({
      type: address.type,
      firstName: address.firstName,
      lastName: address.lastName,
      company: address.company || '',
      address1: address.address1,
      address2: address.address2 || '',
      city: address.city,
      state: address.state,
      postalCode: address.postalCode,
      country: address.country,
      phone: address.phone || '',
      isDefault: address.isDefault,
    });

    // Find and set country, state, city selections for editing
    const country = countries.find(c => c.name === address.country || c.isoCode === address.country);
    if (country) {
      setSelectedCountry(country.isoCode);
      const countryStates = State.getStatesOfCountry(country.isoCode);
      const state = countryStates.find(s => s.name === address.state || s.isoCode === address.state);
      if (state) {
        setSelectedState(state.isoCode);
        const stateCities = City.getCitiesOfState(country.isoCode, state.isoCode);
        const city = stateCities.find(c => c.name === address.city);
        if (city) {
          setSelectedCity(city.name);
        } else {
          setSelectedCity('');
        }
      } else {
        setSelectedState('');
        setSelectedCity('');
      }
    } else {
      setSelectedCountry('');
      setSelectedState('');
      setSelectedCity('');
    }

    setEditingAddress(address);
    setShowAddForm(true);
  };

  const handleDeleteAddress = async (addressId: string) => {
    if (!customer) return;
    
    try {
      setLoading(true);
      const response = await api.deleteAddress(customer.id, addressId);
      if (response.success) {
        toast.success('Address deleted successfully');
        fetchAddresses();
      } else {
        toast.error(response.error || 'Failed to delete address');
      }
    } catch (error) {
      console.error('Failed to delete address:', error);
      toast.error('Failed to delete address');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!customer || !formData.firstName || !formData.lastName || !formData.address1 || !formData.city || !formData.state || !formData.postalCode) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      
      if (editingAddress) {
        const response = await api.updateAddress(customer.id, editingAddress.id, formData);
        if (response.success) {
          toast.success('Address updated successfully');
        } else {
          toast.error(response.error || 'Failed to update address');
          return;
        }
      } else {
        const response = await api.createAddress(customer.id, formData);
        if (response.success) {
          toast.success('Address added successfully');
        } else {
          toast.error(response.error || 'Failed to add address');
          return;
        }
      }
      setShowAddForm(false);
      setEditingAddress(null);
      fetchAddresses();
    } catch (error) {
      console.error('Failed to save address:', error);
      toast.error('Failed to save address');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof AddressFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle country selection
  const handleCountryChange = (countryCode: string) => {
    setSelectedCountry(countryCode);
    setSelectedState('');
    setSelectedCity('');

    const country = countries.find(c => c.isoCode === countryCode);
    handleInputChange('country', country?.name || '');
    handleInputChange('state', '');
    handleInputChange('city', '');
  };

  // Handle state selection
  const handleStateChange = (stateCode: string) => {
    setSelectedState(stateCode);
    setSelectedCity('');

    const state = states.find(s => s.isoCode === stateCode);
    handleInputChange('state', state?.name || '');
    handleInputChange('city', '');
  };

  // Handle city selection
  const handleCityChange = (cityName: string) => {
    setSelectedCity(cityName);
    handleInputChange('city', cityName);
  };

  const getAddressTypeIcon = (type: string) => {
    return type === 'BILLING' ? <Building className="h-4 w-4" /> : <Home className="h-4 w-4" />;
  };

  if (!customer) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Addresses</DialogTitle>
          <DialogDescription>
            Manage addresses for {customer.firstName} {customer.lastName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Address List */}
          {!showAddForm && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Addresses ({addresses.length})</h3>
                <Button onClick={handleAddAddress} size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Address
                </Button>
              </div>

              {addresses.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-8">
                    <MapPin className="h-12 w-12 text-muted-foreground mb-4" />
                    <p className="text-muted-foreground text-center">
                      No addresses found for this customer.
                    </p>
                    <Button onClick={handleAddAddress} className="mt-4">
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Address
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-3">
                  {addresses.map((address) => (
                    <Card key={address.id}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {getAddressTypeIcon(address.type)}
                            <CardTitle className="text-base">
                              {address.type} Address
                            </CardTitle>
                            {address.isDefault && (
                              <Badge variant="default">Default</Badge>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditAddress(address)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteAddress(address.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-2 text-sm">
                          <div>
                            <strong>{address.firstName} {address.lastName}</strong>
                            {address.company && (
                              <span className="text-muted-foreground"> • {address.company}</span>
                            )}
                          </div>
                          <div>{address.address1}</div>
                          {address.address2 && <div>{address.address2}</div>}
                          <div>
                            {address.city}, {address.state} {address.postalCode}
                          </div>
                          <div>{address.country}</div>
                          {address.phone && <div>{address.phone}</div>}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Add/Edit Address Form */}
          {showAddForm && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">
                  {editingAddress ? 'Edit Address' : 'Add New Address'}
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setShowAddForm(false);
                    setEditingAddress(null);
                  }}
                >
                  Cancel
                </Button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">Address Type *</Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value: 'BILLING' | 'SHIPPING') => handleInputChange('type', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="BILLING">Billing Address</SelectItem>
                        <SelectItem value="SHIPPING">Shipping Address</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="isDefault">Default Address</Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isDefault"
                        checked={formData.isDefault}
                        onCheckedChange={(checked) => handleInputChange('isDefault', checked)}
                      />
                      <Label htmlFor="isDefault" className="text-sm">
                        Set as default {formData.type.toLowerCase()} address
                      </Label>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address1">Address Line 1 *</Label>
                  <Input
                    id="address1"
                    value={formData.address1}
                    onChange={(e) => handleInputChange('address1', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address2">Address Line 2</Label>
                  <Input
                    id="address2"
                    value={formData.address2}
                    onChange={(e) => handleInputChange('address2', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">Country *</Label>
                  <Select value={selectedCountry} onValueChange={handleCountryChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      {countries.map((country) => (
                        <SelectItem key={country.isoCode} value={country.isoCode}>
                          <div className="flex items-center gap-2">
                            <span>{country.flag}</span>
                            <span>{country.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="state">State/Province *</Label>
                    <Select
                      value={selectedState}
                      onValueChange={handleStateChange}
                      disabled={states.length === 0}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={states.length === 0 ? "Select country first" : "Select state/province"} />
                      </SelectTrigger>
                      <SelectContent className="max-h-60">
                        {states.map((state) => (
                          <SelectItem key={state.isoCode} value={state.isoCode}>
                            {state.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="city">City *</Label>
                    <Select
                      value={selectedCity}
                      onValueChange={handleCityChange}
                      disabled={cities.length === 0}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={cities.length === 0 ? "Select state first" : "Select city"} />
                      </SelectTrigger>
                      <SelectContent className="max-h-60">
                        {cities.map((city) => (
                          <SelectItem key={city.name} value={city.name}>
                            {city.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="postalCode">Postal Code *</Label>
                  <Input
                    id="postalCode"
                    value={formData.postalCode}
                    onChange={(e) => handleInputChange('postalCode', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <PhoneInputWithFlag
                    id="phone"
                    placeholder="Enter phone number"
                    value={formData.phone}
                    onChange={(value) => handleInputChange('phone', value)}
                  />
                </div>

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowAddForm(false);
                      setEditingAddress(null);
                    }}
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Saving...' : (editingAddress ? 'Update Address' : 'Add Address')}
                  </Button>
                </DialogFooter>
              </form>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 