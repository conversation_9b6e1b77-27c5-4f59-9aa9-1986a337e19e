name: Build and Push Next.js Docker Image

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Next.js image
        uses: docker/build-push-action@v5
        with:
          context: ./nextjs-frontend
          file: ./nextjs-frontend/Dockerfile
          push: true
          tags: harshitdkanodia/peptides_nextjs:latest
