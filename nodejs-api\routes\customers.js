const express = require('express');
const { body, param, query } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const validateRequest = require('../middleware/validateRequest');
const { asyncHandler } = require('../middleware/errorHandler');
const { requireRole, requirePermission } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Get all customers with pagination and filters
router.get('/', requirePermission('CUSTOMERS', 'READ'), [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('customerType').optional().isIn(['B2C', 'B2B', 'ENTERPRISE']).withMessage('Invalid customer type'),
  query('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
  query('search').optional().isString().withMessage('Search must be a string'),
  validateRequest
], asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    customerType,
    isActive,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Build where clause
  const where = {};
  if (customerType) where.customerType = customerType;
  if (isActive !== undefined) where.isActive = isActive === 'true';
  if (search) {
    where.OR = [
      { firstName: { contains: search, mode: 'insensitive' } },
      { lastName: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { mobile: { contains: search, mode: 'insensitive' } }
    ];
  }

  // Get customers and total count
  const [customers, total] = await Promise.all([
    prisma.customer.findMany({
      where,
      skip,
      take: parseInt(limit),
      orderBy: { [sortBy]: sortOrder },
      include: {
        addresses: {
          select: {
            id: true,
            type: true,
            city: true,
            state: true,
            country: true,
            isDefault: true
          }
        },
        customerTags: {
          select: {
            id: true,
            tag: true
          }
        },
        _count: {
          select: {
            orders: true,
            addresses: true,
            reviews: true
          }
        }
      }
    }),
    prisma.customer.count({ where })
  ]);

  res.json({
    success: true,
    data: {
      customers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    }
  });
}));

// Get customer by ID
router.get('/:id', requirePermission('CUSTOMERS', 'READ'), [
  param('id').isString().withMessage('Customer ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;

  const customer = await prisma.customer.findUnique({
    where: { id },
    include: {
      addresses: {
        orderBy: { createdAt: 'desc' }
      },
      customerTags: {
        select: {
          id: true,
          tag: true
        }
      },
      orders: {
        select: {
          id: true,
          orderNumber: true,
          status: true,
          totalAmount: true,
          createdAt: true
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      },
      reviews: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              images: {
                select: {
                  url: true,
                  altText: true
                },
                take: 1,
                orderBy: { sortOrder: 'asc' }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      },
      _count: {
        select: {
          orders: true,
          addresses: true,
          reviews: true
        }
      }
    }
  });

  if (!customer) {
    return res.status(404).json({
      success: false,
      error: 'Customer not found'
    });
  }

  res.json({
    success: true,
    data: customer
  });
}));

// Create new customer
router.post('/', requirePermission('CUSTOMERS', 'CREATE'), [
  body('firstName').notEmpty().trim().withMessage('First name is required'),
  body('lastName').notEmpty().trim().withMessage('Last name is required'),
  body('middleName').optional().trim(),
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('mobile').notEmpty().trim().withMessage('Mobile number is required'),
  body('customerType').optional().isIn(['B2C', 'B2B', 'ENTERPRISE']).withMessage('Invalid customer type'),
  body('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
  body('tags').optional().isArray().withMessage('Tags must be an array'),
  body('addresses').optional().isArray().withMessage('Addresses must be an array'),
  body('addresses.*.type').optional().isIn(['BILLING', 'SHIPPING']).withMessage('Invalid address type'),
  body('addresses.*.firstName').optional().notEmpty().withMessage('Address first name is required'),
  body('addresses.*.lastName').optional().notEmpty().withMessage('Address last name is required'),
  body('addresses.*.address1').optional().notEmpty().withMessage('Address line 1 is required'),
  body('addresses.*.city').optional().notEmpty().withMessage('City is required'),
  body('addresses.*.state').optional().notEmpty().withMessage('State is required'),
  body('addresses.*.postalCode').optional().notEmpty().withMessage('Postal code is required'),
  body('addresses.*.country').optional().notEmpty().withMessage('Country is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const {
    firstName,
    lastName,
    middleName,
    email,
    mobile,
    customerType = 'B2C',
    isActive = true,
    tags = [],
    addresses = []
  } = req.body;

  // Check if customer already exists
  const existingCustomer = await prisma.customer.findFirst({
    where: {
      OR: [
        { email },
        { mobile }
      ]
    }
  });

  if (existingCustomer) {
    return res.status(409).json({
      success: false,
      error: 'Customer already exists with this email or mobile number'
    });
  }

  // Create customer with addresses and tags in transaction
  const customer = await prisma.$transaction(async (tx) => {
    // Create customer
    const newCustomer = await tx.customer.create({
      data: {
        firstName,
        lastName,
        middleName,
        email,
        mobile,
        customerType,
        isActive
      }
    });

    // Create tags
    if (tags.length > 0) {
      await tx.customerTag.createMany({
        data: tags.map(tag => ({
          customerId: newCustomer.id,
          tag
        }))
      });
    }

    // Create addresses
    if (addresses.length > 0) {
      await tx.address.createMany({
        data: addresses.map((addr, index) => ({
          customerId: newCustomer.id,
          type: addr.type || 'BILLING',
          firstName: addr.firstName || firstName,
          lastName: addr.lastName || lastName,
          company: addr.company,
          address1: addr.address1,
          address2: addr.address2,
          city: addr.city,
          state: addr.state,
          postalCode: addr.postalCode,
          country: addr.country || 'US',
          phone: addr.phone,
          isDefault: index === 0 // First address is default
        }))
      });
    }

    return newCustomer;
  });

  // Fetch complete customer with relations
  const completeCustomer = await prisma.customer.findUnique({
    where: { id: customer.id },
    include: {
      addresses: true,
      customerTags: {
        select: {
          id: true,
          tag: true
        }
      }
    }
  });

  res.status(201).json({
    success: true,
    message: 'Customer created successfully',
    data: completeCustomer
  });
}));

// Update customer
router.put('/:id', requirePermission('CUSTOMERS', 'UPDATE'), [
  param('id').isString().withMessage('Customer ID is required'),
  body('firstName').optional().notEmpty().trim().withMessage('First name cannot be empty'),
  body('lastName').optional().notEmpty().trim().withMessage('Last name cannot be empty'),
  body('middleName').optional().trim(),
  body('email').optional().isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('mobile').optional().notEmpty().trim().withMessage('Mobile number cannot be empty'),
  body('customerType').optional().isIn(['B2C', 'B2B', 'ENTERPRISE']).withMessage('Invalid customer type'),
  body('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
  body('tags').optional().isArray().withMessage('Tags must be an array'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { firstName, lastName, middleName, email, mobile, customerType, isActive, tags } = req.body;

  // Check if customer exists
  const existingCustomer = await prisma.customer.findUnique({
    where: { id }
  });

  if (!existingCustomer) {
    return res.status(404).json({
      success: false,
      error: 'Customer not found'
    });
  }

  // Check if email or mobile is already taken by another customer
  if (email || mobile) {
    const conflicts = await prisma.customer.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          {
            OR: [
              email ? { email } : {},
              mobile ? { mobile } : {}
            ].filter(condition => Object.keys(condition).length > 0)
          }
        ]
      }
    });

    if (conflicts) {
      return res.status(409).json({
        success: false,
        error: 'Email or mobile number is already taken by another customer'
      });
    }
  }

  // Update customer in transaction
  const customer = await prisma.$transaction(async (tx) => {
    // Update customer basic info
    const updateData = {};
    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (middleName !== undefined) updateData.middleName = middleName;
    if (email) updateData.email = email;
    if (mobile) updateData.mobile = mobile;
    if (customerType) updateData.customerType = customerType;
    if (isActive !== undefined) updateData.isActive = isActive;

    await tx.customer.update({
      where: { id },
      data: updateData
    });

    // Update tags
    if (tags) {
      await tx.customerTag.deleteMany({
        where: { customerId: id }
      });
      
      if (tags.length > 0) {
        await tx.customerTag.createMany({
          data: tags.map(tag => ({
            customerId: id,
            tag
          }))
        });
      }
    }

    return tx.customer.findUnique({
      where: { id },
      include: {
        addresses: true,
        customerTags: {
          select: {
            id: true,
            tag: true
          }
        }
      }
    });
  });

  res.json({
    success: true,
    message: 'Customer updated successfully',
    data: customer
  });
}));

// Delete customer (soft delete)
router.delete('/:id', requirePermission('CUSTOMERS', 'DELETE'), [
  param('id').isString().withMessage('Customer ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if customer exists
  const existingCustomer = await prisma.customer.findUnique({
    where: { id }
  });

  if (!existingCustomer) {
    return res.status(404).json({
      success: false,
      error: 'Customer not found'
    });
  }

  // Soft delete by setting isActive to false
  await prisma.customer.update({
    where: { id },
    data: { isActive: false }
  });

  res.json({
    success: true,
    message: 'Customer deactivated successfully'
  });
}));

// Bulk delete customers
router.post(
  "/bulk-delete",
  requirePermission("CUSTOMERS", "DELETE"),
  [body("ids").isArray({ min: 1 }).withMessage("ids must be a non-empty array"), validateRequest],
  asyncHandler(async (req, res) => {
    const { ids } = req.body;
    const result = await prisma.customer.updateMany({
      where: { id: { in: ids } },
      data: { isActive: false },
    });
    res.json({ success: true, deactivated: result.count });
  })
);

// Bulk import customers
router.post(
  "/bulk-import",
  requirePermission("CUSTOMERS", "CREATE"),
  [body("customers").isArray({ min: 1 }).withMessage("customers must be a non-empty array"), validateRequest],
  asyncHandler(async (req, res) => {
    const { customers } = req.body;
    let created = 0;
    await prisma.$transaction(async (tx) => {
      for (const c of customers) {
        if (!c.firstName || !c.lastName || !c.email) continue;
        await tx.customer.create({
          data: {
            firstName: c.firstName,
            lastName: c.lastName,
            email: c.email,
            phone: c.phone || '',
            type: c.type || 'B2C',
            isActive: true,
          },
        });
        created++;
      }
    });
    res.json({ success: true, created });
  })
);

// Get customer addresses
router.get('/:id/addresses', requirePermission('CUSTOMERS', 'READ'), [
  param('id').isString().withMessage('Customer ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if customer exists
  const existingCustomer = await prisma.customer.findUnique({
    where: { id }
  });

  if (!existingCustomer) {
    return res.status(404).json({
      success: false,
      error: 'Customer not found'
    });
  }

  // Get addresses
  const addresses = await prisma.address.findMany({
    where: { customerId: id },
    orderBy: { createdAt: 'desc' }
  });

  res.json({
    success: true,
    data: addresses
  });
}));

// Create address for customer
router.post('/:id/addresses', requirePermission('CUSTOMERS', 'CREATE'), [
  param('id').isString().withMessage('Customer ID is required'),
  body('type').isIn(['BILLING', 'SHIPPING']).withMessage('Invalid address type'),
  body('firstName').notEmpty().trim().withMessage('First name is required'),
  body('lastName').notEmpty().trim().withMessage('Last name is required'),
  body('address1').notEmpty().trim().withMessage('Address line 1 is required'),
  body('city').notEmpty().trim().withMessage('City is required'),
  body('state').notEmpty().trim().withMessage('State is required'),
  body('postalCode').notEmpty().trim().withMessage('Postal code is required'),
  body('country').notEmpty().trim().withMessage('Country is required'),
  body('isDefault').optional().isBoolean().withMessage('isDefault must be boolean'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    type,
    firstName,
    lastName,
    company,
    address1,
    address2,
    city,
    state,
    postalCode,
    country,
    phone,
    isDefault = false
  } = req.body;

  // Check if customer exists
  const existingCustomer = await prisma.customer.findUnique({
    where: { id }
  });

  if (!existingCustomer) {
    return res.status(404).json({
      success: false,
      error: 'Customer not found'
    });
  }

  // Create address in transaction
  const address = await prisma.$transaction(async (tx) => {
    // If this is set as default, unset other defaults
    if (isDefault) {
      await tx.address.updateMany({
        where: { customerId: id },
        data: { isDefault: false }
      });
    }

    return tx.address.create({
      data: {
        customerId: id,
        type,
        firstName,
        lastName,
        company,
        address1,
        address2,
        city,
        state,
        postalCode,
        country,
        phone,
        isDefault
      }
    });
  });

  res.status(201).json({
    success: true,
    message: 'Address created successfully',
    data: address
  });
}));

// Update address
router.put('/:customerId/addresses/:addressId', requirePermission('CUSTOMERS', 'UPDATE'), [
  param('customerId').isString().withMessage('Customer ID is required'),
  param('addressId').isString().withMessage('Address ID is required'),
  body('type').optional().isIn(['BILLING', 'SHIPPING']).withMessage('Invalid address type'),
  body('firstName').optional().notEmpty().trim().withMessage('First name cannot be empty'),
  body('lastName').optional().notEmpty().trim().withMessage('Last name cannot be empty'),
  body('address1').optional().notEmpty().trim().withMessage('Address line 1 cannot be empty'),
  body('city').optional().notEmpty().trim().withMessage('City cannot be empty'),
  body('state').optional().notEmpty().trim().withMessage('State cannot be empty'),
  body('postalCode').optional().notEmpty().trim().withMessage('Postal code cannot be empty'),
  body('country').optional().notEmpty().trim().withMessage('Country cannot be empty'),
  body('isDefault').optional().isBoolean().withMessage('isDefault must be boolean'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { customerId, addressId } = req.params;
  const {
    type,
    firstName,
    lastName,
    company,
    address1,
    address2,
    city,
    state,
    postalCode,
    country,
    phone,
    isDefault
  } = req.body;

  // Check if address exists and belongs to customer
  const existingAddress = await prisma.address.findUnique({
    where: { id: addressId, customerId }
  });

  if (!existingAddress) {
    return res.status(404).json({
      success: false,
      error: 'Address not found'
    });
  }

  // Update address in transaction
  const address = await prisma.$transaction(async (tx) => {
    // If this is set as default, unset other defaults
    if (isDefault) {
      await tx.address.updateMany({
        where: { customerId },
        data: { isDefault: false }
      });
    }

    // Update address
    const updateData = {};
    if (type) updateData.type = type;
    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (company !== undefined) updateData.company = company;
    if (address1) updateData.address1 = address1;
    if (address2 !== undefined) updateData.address2 = address2;
    if (city) updateData.city = city;
    if (state) updateData.state = state;
    if (postalCode) updateData.postalCode = postalCode;
    if (country) updateData.country = country;
    if (phone !== undefined) updateData.phone = phone;
    if (isDefault !== undefined) updateData.isDefault = isDefault;

    return tx.address.update({
      where: { id: addressId },
      data: updateData
    });
  });

  res.json({
    success: true,
    message: 'Address updated successfully',
    data: address
  });
}));

// Delete address
router.delete('/:customerId/addresses/:addressId', requirePermission('CUSTOMERS', 'DELETE'), [
  param('customerId').isString().withMessage('Customer ID is required'),
  param('addressId').isString().withMessage('Address ID is required'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { customerId, addressId } = req.params;

  // Check if address exists and belongs to customer
  const existingAddress = await prisma.address.findUnique({
    where: { id: addressId, customerId }
  });

  if (!existingAddress) {
    return res.status(404).json({
      success: false,
      error: 'Address not found'
    });
  }

  // Delete address
  await prisma.address.delete({
    where: { id: addressId }
  });

  res.json({
    success: true,
    message: 'Address deleted successfully'
  });
}));

// Get customer orders
router.get('/:id/orders', requirePermission('CUSTOMERS', 'READ'), [
  param('id').isString().withMessage('Customer ID is required'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  validateRequest
], asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { page = 1, limit = 10 } = req.query;

  // Check if customer exists
  const existingCustomer = await prisma.customer.findUnique({
    where: { id }
  });

  if (!existingCustomer) {
    return res.status(404).json({
      success: false,
      error: 'Customer not found'
    });
  }

  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Get orders and total count
  const [orders, total] = await Promise.all([
    prisma.order.findMany({
      where: { customerId: id },
      skip,
      take: parseInt(limit),
      orderBy: { createdAt: 'desc' },
      include: {
        items: {
          include: {
            variant: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    images: {
                      select: {
                        url: true,
                        altText: true
                      },
                      take: 1,
                      orderBy: { sortOrder: 'asc' }
                    }
                  }
                }
              }
            }
          }
        },
        payments: {
          select: {
            id: true,
            paymentMethod: true,
            status: true,
            amount: true,
            paidAt: true
          }
        }
      }
    }),
    prisma.order.count({ where: { customerId: id } })
  ]);

  res.json({
    success: true,
    data: {
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    }
  });
}));

module.exports = router;