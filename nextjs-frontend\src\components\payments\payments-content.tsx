"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    CreditCard,
    DollarSign,
    TrendingUp,
    AlertTriangle,
    CheckCircle,
    XCircle,
    Clock,
    RefreshCw,
    MoreHorizontal,
    Plus,
    Settings,
    Shield,
    Zap
} from "lucide-react";
import { api } from "@/lib/api";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { useForm } from "react-hook-form";
import { getToken } from "@/lib/api";

// Mock data for transactions
const transactions = [
    {
        id: "TXN-001",
        orderId: "ORD-1234",
        customer: "Dr. Sarah Wilson",
        amount: 299.99,
        currency: "USD",
        method: "Credit Card",
        status: "Completed",
        gateway: "Stripe",
        transactionId: "pi_1234567890",
        createdAt: "2024-06-16T10:30:00Z",
        completedAt: "2024-06-16T10:30:15Z",
    },
    {
        id: "TXN-002",
        orderId: "ORD-1235",
        customer: "Prof. Michael Chen",
        amount: 156.50,
        currency: "USD",
        method: "PayPal",
        status: "Completed",
        gateway: "PayPal",
        transactionId: "PAYID-ABC123",
        createdAt: "2024-06-16T09:15:00Z",
        completedAt: "2024-06-16T09:15:08Z",
    },
    {
        id: "TXN-003",
        orderId: "ORD-1236",
        customer: "Dr. Emily Rodriguez",
        amount: 445.00,
        currency: "USD",
        method: "Bank Transfer",
        status: "Pending",
        gateway: "Manual",
        transactionId: "BT-789012",
        createdAt: "2024-06-16T08:45:00Z",
        completedAt: null,
    },
    {
        id: "TXN-004",
        orderId: "ORD-1237",
        customer: "Dr. James Parker",
        amount: 89.99,
        currency: "USD",
        method: "Credit Card",
        status: "Failed",
        gateway: "Stripe",
        transactionId: "pi_failed123",
        createdAt: "2024-06-16T07:20:00Z",
        completedAt: null,
    },
];

// Mock data for payment methods
const paymentMethods = [
    {
        id: 1,
        name: "Stripe",
        type: "Credit Cards",
        isActive: true,
        status: "Connected",
        fees: "2.9% + $0.30",
        lastSync: "2024-06-16T12:00:00Z",
        totalProcessed: 45620.50,
    },
    {
        id: 2,
        name: "PayPal",
        type: "Digital Wallet",
        isActive: true,
        status: "Connected",
        fees: "3.49% + $0.49",
        lastSync: "2024-06-16T11:45:00Z",
        totalProcessed: 12450.75,
    },
    {
        id: 3,
        name: "Bank Transfer",
        type: "Direct Transfer",
        isActive: true,
        status: "Manual",
        fees: "$2.50 flat",
        lastSync: null,
        totalProcessed: 8900.00,
    },
    {
        id: 4,
        name: "Apple Pay",
        type: "Mobile Payment",
        isActive: false,
        status: "Not Connected",
        fees: "2.9% + $0.30",
        lastSync: null,
        totalProcessed: 0,
    },
];

const StatusBadge = ({ status }: { status: string }) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
        Completed: "default",
        Pending: "secondary",
        Failed: "destructive",
        Refunded: "outline",
    };

    const icons: { [key: string]: React.ReactNode } = {
        Completed: <CheckCircle className="h-3 w-3" />,
        Pending: <Clock className="h-3 w-3" />,
        Failed: <XCircle className="h-3 w-3" />,
        Refunded: <RefreshCw className="h-3 w-3" />,
    };

    return (
        <Badge variant={variants[status]} className="flex items-center gap-1">
            {icons[status]}
            {status}
        </Badge>
    );
};

const GatewayStatus = ({ status }: { status: string }) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
        Connected: "default",
        "Not Connected": "destructive",
        Manual: "secondary",
    };

    return <Badge variant={variants[status]}>{status}</Badge>;
};

export function PaymentsContent() {
    const [selectedTransaction, setSelectedTransaction] = useState<string | null>(null);
    const [transactions, setTransactions] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [recordDialogOpen, setRecordDialogOpen] = useState(false);
    const [orders, setOrders] = useState<any[]>([]);
    const [customers, setCustomers] = useState<any[]>([]);
    const [submitting, setSubmitting] = useState(false);

    // Fetch orders and customers for the form
    useEffect(() => {
        if (recordDialogOpen) {
            api.getOrders({ limit: 100 }).then(res => {
                console.log('Orders API response:', res);
                if (res.success && res.data) {
                    // Handle both possible response structures
                    const ordersData = Array.isArray(res.data) ? res.data : res.data.orders || [];
                    setOrders(ordersData);
                }
            });
            api.getCustomers({ limit: 100 }).then(res => {
                console.log('Customers API response:', res);
                if (res.success && res.data) {
                    // Handle both possible response structures
                    const customersData = Array.isArray(res.data) ? res.data : res.data.customers || [];
                    setCustomers(customersData);
                }
            });
        }
    }, [recordDialogOpen]);

    useEffect(() => {
        setLoading(true);
        setError(null);
        const token = getToken();
        console.log('[Payments] Token in localStorage:', token);
        api.getTransactions()
            .then((res: any) => {
                console.log('[Payments] /transactions API response:', res);
                if (res.success && res.data) {
                    // Backend returns transactions directly in res.data, not res.data.transactions
                    const transactionsData = Array.isArray(res.data) ? res.data : res.data.transactions || [];
                    setTransactions(transactionsData);
                } else {
                    setError(res.error || "Failed to load transactions");
                    // Only show toast if API call actually failed
                    if (!res.success) {
                        toast.error(res.error || "Failed to load transactions");
                    }
                }
            })
            .catch((e: any) => {
                console.error('[Payments] Exception while loading transactions:', e);
                setError(e.message || "Failed to load transactions");
                toast.error(e.message || "Failed to load transactions");
            })
            .finally(() => setLoading(false));
    }, []);

    const totalRevenue = transactions
        .filter(t => t.paymentStatus === "COMPLETED")
        .reduce((sum, t) => sum + Number(t.amount), 0);

    const pendingAmount = transactions
        .filter(t => t.paymentStatus === "PENDING")
        .reduce((sum, t) => sum + Number(t.amount), 0);

    const failedCount = transactions.filter(t => t.paymentStatus === "FAILED").length;

    const form = useForm({
        defaultValues: {
            orderId: "",
            amount: "",
            paymentMethod: "",
            paymentGatewayName: "DIRECT",
            paymentStatus: "COMPLETED",
            paymentGatewayTransactionId: ""
        }
    });
    const { register, handleSubmit, reset, setValue } = form;

    const onSubmit = async (data: any) => {
        setSubmitting(true);
        try {
            console.log('Form data submitted:', data);

            // Validate required fields
            if (!data.orderId || !data.amount || !data.paymentGatewayName || !data.paymentStatus) {
                console.log('Missing required fields:', {
                    orderId: data.orderId,
                    amount: data.amount,
                    paymentGatewayName: data.paymentGatewayName,
                    paymentStatus: data.paymentStatus
                });
                toast.error('Please fill in all required fields');
                setSubmitting(false);
                return;
            }

            // Validate amount is a positive number
            const amount = parseFloat(data.amount);
            if (isNaN(amount) || amount <= 0) {
                toast.error('Please enter a valid amount');
                setSubmitting(false);
                return;
            }

            const order = orders.find((o: any) => o.id === data.orderId);
            const payload = {
                orderId: data.orderId,
                amount: amount.toFixed(2),
                paymentGatewayName: data.paymentGatewayName,
                paymentStatus: data.paymentStatus,
                paymentGatewayTransactionId: data.paymentGatewayTransactionId || undefined,
                paymentGatewayResponse: undefined,
            };

            console.log('Payload being sent:', payload);
            const res = await api.createTransaction(payload);
            if (res.success) {
                toast.success("Payment recorded");
                setRecordDialogOpen(false);
                reset();
                // Refresh transactions
                setLoading(true);
                api.getTransactions().then((res: any) => {
                    if (res.success && res.data) {
                        const transactionsData = Array.isArray(res.data) ? res.data : res.data.transactions || [];
                        setTransactions(transactionsData);
                    }
                    setLoading(false);
                });
            } else {
                toast.error(res.error || "Failed to record payment");
            }
        } catch (e: any) {
            toast.error(e.message || "Failed to record payment");
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Payments</h1>
                    <p className="text-muted-foreground">
                        Manage payment processing, transactions, and gateway settings.
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button variant="outline">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Sync All Gateways
                    </Button>
                    <Button onClick={() => setRecordDialogOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" /> Record Payment
                    </Button>
                    <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Payment Method
                    </Button>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">${totalRevenue.toLocaleString()}</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +12.5% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">${pendingAmount.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">
                            Awaiting processing
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                        <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">96.8%</div>
                        <div className="flex items-center text-xs text-muted-foreground">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                            +2.1% from last month
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Failed Transactions</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{failedCount}</div>
                        <p className="text-xs text-muted-foreground">
                            Requiring attention
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Tabs */}
            <Tabs defaultValue="transactions" className="space-y-6">
                <TabsList>
                    <TabsTrigger value="transactions">Transactions</TabsTrigger>
                    <TabsTrigger value="gateways">Payment Gateways</TabsTrigger>
                    <TabsTrigger value="settings">Settings</TabsTrigger>
                    <TabsTrigger value="reports">Reports</TabsTrigger>
                </TabsList>

                {/* Transactions Tab */}
                <TabsContent value="transactions" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle>Recent Transactions</CardTitle>
                                    <CardDescription>
                                        View and manage payment transactions
                                    </CardDescription>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Select>
                                        <SelectTrigger className="w-32">
                                            <SelectValue placeholder="Filter" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Status</SelectItem>
                                            <SelectItem value="completed">Completed</SelectItem>
                                            <SelectItem value="pending">Pending</SelectItem>
                                            <SelectItem value="failed">Failed</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Transaction ID</TableHead>
                                        <TableHead>Order</TableHead>
                                        <TableHead>Customer</TableHead>
                                        <TableHead>Amount</TableHead>
                                        <TableHead>Method</TableHead>
                                        <TableHead>Gateway</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Date</TableHead>
                                        <TableHead></TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {loading ? (
                                        <TableRow><TableCell colSpan={9}>Loading...</TableCell></TableRow>
                                    ) : error && (transactions === null || transactions === undefined) ? (
                                        <TableRow><TableCell colSpan={9}>Failed to load transactions.</TableCell></TableRow>
                                    ) : Array.isArray(transactions) && transactions.length === 0 ? (
                                        <TableRow><TableCell colSpan={9}>No transactions found.</TableCell></TableRow>
                                    ) : (
                                        transactions.map((transaction) => (
                                            <TableRow key={transaction.id}>
                                                <TableCell className="font-medium">{transaction.id}</TableCell>
                                                <TableCell>{transaction.orderId}</TableCell>
                                                <TableCell>
                                                    {transaction.order?.customer
                                                        ? `${transaction.order.customer.firstName} ${transaction.order.customer.lastName}`
                                                        : 'N/A'
                                                    }
                                                </TableCell>
                                                <TableCell>${parseFloat(transaction.amount).toFixed(2)}</TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-2">
                                                        <CreditCard className="h-4 w-4" />
                                                        {transaction.paymentGatewayName}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant="outline">{transaction.paymentGatewayName}</Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <StatusBadge status={transaction.paymentStatus} />
                                                </TableCell>
                                                <TableCell>
                                                    {new Date(transaction.createdAt).toLocaleDateString()}
                                                </TableCell>
                                                <TableCell>
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                                <MoreHorizontal className="h-4 w-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuItem>
                                                                View Details
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem>
                                                                Download Receipt
                                                            </DropdownMenuItem>
                                                            {transaction.paymentStatus === "COMPLETED" && (
                                                                <>
                                                                    <DropdownMenuSeparator />
                                                                    <DropdownMenuItem>
                                                                        <RefreshCw className="h-4 w-4 mr-2" />
                                                                        Process Refund
                                                                    </DropdownMenuItem>
                                                                </>
                                                            )}
                                                            {transaction.paymentStatus === "FAILED" && (
                                                                <>
                                                                    <DropdownMenuSeparator />
                                                                    <DropdownMenuItem>
                                                                        Retry Payment
                                                                    </DropdownMenuItem>
                                                                </>
                                                            )}
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </TabsContent>

                {/* Payment Gateways Tab */}
                <TabsContent value="gateways" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        {paymentMethods.map((method) => (
                            <Card key={method.id}>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="h-12 w-12 bg-muted rounded-lg flex items-center justify-center">
                                                <CreditCard className="h-6 w-6" />
                                            </div>
                                            <div>
                                                <CardTitle className="text-lg">{method.name}</CardTitle>
                                                <CardDescription>{method.type}</CardDescription>
                                            </div>
                                        </div>
                                        <GatewayStatus status={method.status} />
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <Label className="text-muted-foreground">Processing Fees</Label>
                                            <p className="font-medium">{method.fees}</p>
                                        </div>
                                        <div>
                                            <Label className="text-muted-foreground">Total Processed</Label>
                                            <p className="font-medium">${method.totalProcessed.toLocaleString()}</p>
                                        </div>
                                    </div>

                                    {method.lastSync && (
                                        <div className="text-sm">
                                            <Label className="text-muted-foreground">Last Sync</Label>
                                            <p>{new Date(method.lastSync).toLocaleString()}</p>
                                        </div>
                                    )}

                                    <div className="flex items-center justify-between pt-2">
                                        <div className="flex items-center space-x-2">
                                            <Switch checked={method.isActive} />
                                            <Label>Active</Label>
                                        </div>
                                        <Button variant="outline" size="sm">
                                            <Settings className="h-4 w-4 mr-2" />
                                            Configure
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </TabsContent>

                {/* Settings Tab */}
                <TabsContent value="settings" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Payment Settings</CardTitle>
                                <CardDescription>
                                    Configure global payment preferences
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="currency">Default Currency</Label>
                                    <Select>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select currency" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="usd">USD - US Dollar</SelectItem>
                                            <SelectItem value="eur">EUR - Euro</SelectItem>
                                            <SelectItem value="gbp">GBP - British Pound</SelectItem>
                                            <SelectItem value="cad">CAD - Canadian Dollar</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="payment-timeout">Payment Timeout (minutes)</Label>
                                    <Input
                                        id="payment-timeout"
                                        type="number"
                                        placeholder="15"
                                        defaultValue="15"
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="auto-capture">Auto-capture payments</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Automatically capture authorized payments
                                        </p>
                                    </div>
                                    <Switch id="auto-capture" defaultChecked />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="save-cards">Allow saving payment methods</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Let customers save cards for future purchases
                                        </p>
                                    </div>
                                    <Switch id="save-cards" defaultChecked />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="partial-payments">Allow partial payments</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Enable installment or partial payment options
                                        </p>
                                    </div>
                                    <Switch id="partial-payments" />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Security & Compliance</CardTitle>
                                <CardDescription>
                                    Payment security and compliance settings
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                                    <Shield className="h-5 w-5 text-green-600" />
                                    <div>
                                        <p className="font-medium text-green-800">PCI DSS Compliant</p>
                                        <p className="text-sm text-green-600">Your payment processing is secure</p>
                                    </div>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="fraud-detection">Enable fraud detection</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Automatically flag suspicious transactions
                                        </p>
                                    </div>
                                    <Switch id="fraud-detection" defaultChecked />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <Label htmlFor="3d-secure">Require 3D Secure</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Additional authentication for card payments
                                        </p>
                                    </div>
                                    <Switch id="3d-secure" />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="webhook-url">Webhook URL</Label>
                                    <Input
                                        id="webhook-url"
                                        placeholder="https://your-site.com/webhooks/payments"
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="webhook-secret">Webhook Secret</Label>
                                    <Input
                                        id="webhook-secret"
                                        type="password"
                                        placeholder="Enter webhook secret"
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                {/* Reports Tab */}
                <TabsContent value="reports" className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-3">
                        <Card>
                            <CardHeader>
                                <CardTitle>Quick Reports</CardTitle>
                                <CardDescription>
                                    Generate payment reports
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Button variant="outline" className="w-full justify-start">
                                    <DollarSign className="h-4 w-4 mr-2" />
                                    Daily Revenue Report
                                </Button>
                                <Button variant="outline" className="w-full justify-start">
                                    <TrendingUp className="h-4 w-4 mr-2" />
                                    Monthly Summary
                                </Button>
                                <Button variant="outline" className="w-full justify-start">
                                    <AlertTriangle className="h-4 w-4 mr-2" />
                                    Failed Transactions
                                </Button>
                                <Button variant="outline" className="w-full justify-start">
                                    <RefreshCw className="h-4 w-4 mr-2" />
                                    Refund Report
                                </Button>
                            </CardContent>
                        </Card>

                        <Card className="md:col-span-2">
                            <CardHeader>
                                <CardTitle>Custom Report</CardTitle>
                                <CardDescription>
                                    Generate a custom payment report
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="start-date">Start Date</Label>
                                        <Input id="start-date" type="date" />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="end-date">End Date</Label>
                                        <Input id="end-date" type="date" />
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="report-type">Report Type</Label>
                                    <Select>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select report type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="transactions">Transaction Details</SelectItem>
                                            <SelectItem value="revenue">Revenue Summary</SelectItem>
                                            <SelectItem value="gateway">Gateway Performance</SelectItem>
                                            <SelectItem value="customer">Customer Payment History</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="format">Export Format</Label>
                                    <Select>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select format" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="csv">CSV</SelectItem>
                                            <SelectItem value="xlsx">Excel</SelectItem>
                                            <SelectItem value="pdf">PDF</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <Button className="w-full">
                                    Generate Report
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>

            <Dialog open={recordDialogOpen} onOpenChange={setRecordDialogOpen}>
                <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
                    <DialogHeader><DialogTitle>Record Payment</DialogTitle></DialogHeader>
                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="record-order-id">Order ID</Label>
                                <Select onValueChange={(value) => {
                                    setValue("orderId", value);
                                    // Auto-populate amount when order is selected
                                    const selectedOrder = orders.find(order => order.id === value);
                                    if (selectedOrder) {
                                        setValue("amount", selectedOrder.totalAmount.toString());
                                    }
                                }}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select order" />
                                    </SelectTrigger>
                                    <SelectContent className="max-h-[200px]">
                                        {orders.map((order) => (
                                            <SelectItem key={order.id} value={order.id}>
                                                {order.id.slice(-8)} - {order.customer?.firstName} {order.customer?.lastName} (${parseFloat(order.totalAmount).toFixed(2)})
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="record-amount">Amount</Label>
                                <Input
                                    id="record-amount"
                                    type="number"
                                    step="0.01"
                                    placeholder="0.00"
                                    {...register("amount", { required: true })}
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="record-payment-method">Payment Method</Label>
                                <Select onValueChange={(value) => setValue("paymentMethod", value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select payment method" />
                                    </SelectTrigger>
                                    <SelectContent className="max-h-[200px]">
                                        <SelectItem value="Credit Card">Credit Card</SelectItem>
                                        <SelectItem value="PayPal">PayPal</SelectItem>
                                        <SelectItem value="Bank Transfer">Bank Transfer</SelectItem>
                                        <SelectItem value="Apple Pay">Apple Pay</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="record-gateway">Gateway</Label>
                                <Select
                                    defaultValue="DIRECT"
                                    onValueChange={(value) => setValue("paymentGatewayName", value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select gateway" />
                                    </SelectTrigger>
                                    <SelectContent className="max-h-[200px]">
                                        <SelectItem value="DIRECT">Direct Payment</SelectItem>
                                        <SelectItem value="STRIPE">Stripe</SelectItem>
                                        <SelectItem value="PAYPAL">PayPal</SelectItem>
                                        <SelectItem value="MANUAL">Manual</SelectItem>
                                        <SelectItem value="OTHER">Other</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="record-status">Status</Label>
                                <Select
                                    defaultValue="COMPLETED"
                                    onValueChange={(value) => setValue("paymentStatus", value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                    <SelectContent className="max-h-[200px]">
                                        <SelectItem value="PENDING">Pending</SelectItem>
                                        <SelectItem value="COMPLETED">Completed</SelectItem>
                                        <SelectItem value="FAILED">Failed</SelectItem>
                                        <SelectItem value="CANCELLED">Cancelled</SelectItem>
                                        <SelectItem value="REFUNDED">Refunded</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="record-transaction-id">Transaction ID (if applicable)</Label>
                                <Input
                                    id="record-transaction-id"
                                    {...form.register("paymentGatewayTransactionId")}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setRecordDialogOpen(false)} disabled={submitting}>Cancel</Button>
                            <Button type="submit" disabled={submitting}>{submitting ? "Saving..." : "Record Payment"}</Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
}
