const express = require("express");
const { body, param, query } = require("express-validator");
const { PrismaClient } = require("@prisma/client");
const { v4: uuidv4 } = require("uuid");
const { Country, State } = require("country-state-city");
const validateRequest = require("../middleware/validateRequest");
const { asyncHandler } = require("../middleware/errorHandler");
const { requireRole, requirePermission } = require("../middleware/auth");
const { authMiddleware } = require("../middleware/auth");
const { calculatePromotionDiscount } = require("../utils/promotionCalculator");

const router = express.Router();
const prisma = new PrismaClient();

// Convert country and state names to ISO codes for tax rate lookup
const getCountryStateIsoCodes = (countryName, stateName) => {
  // Find country by name
  const country = Country.getAllCountries().find(c =>
    c.name.toLowerCase() === countryName.toLowerCase()
  );

  if (!country) {
    return { countryCode: null, stateCode: null };
  }

  let stateCode = null;
  if (stateName) {
    // Find state by name within the country
    const state = State.getStatesOfCountry(country.isoCode).find(s =>
      s.name.toLowerCase() === stateName.toLowerCase()
    );
    stateCode = state?.isoCode || null;
  }

  return { countryCode: country.isoCode, stateCode };
};

// Generate unique order number
const generateOrderNumber = () => {
  const timestamp = Date.now().toString(36).toUpperCase();
  const randomStr = Math.random().toString(36).substr(2, 6).toUpperCase();
  return `ORD-${timestamp}-${randomStr}`;
};

// Calculate order totals
const calculateOrderTotals = (items) => {
  const subtotal = items.reduce(
    (sum, item) => sum + item.unitPrice * item.quantity,
    0
  );
  return {
    subtotal,
    // These can be calculated based on business logic
    discountAmount: 0,
    shippingAmount: 0,
    taxAmount: subtotal * 0.08, // 8% tax example
    totalAmount: subtotal + subtotal * 0.08,
  };
};

// Get all orders with pagination and filters
router.get(
  "/",
  requirePermission("ORDERS", "READ"),
  [
    query("page")
      .optional()
      .isInt({ min: 1 })
      .withMessage("Page must be a positive integer"),
    query("limit")
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage("Limit must be between 1 and 100"),
    query("status")
      .optional()
      .isIn([
        "PENDING",
        "PROCESSING",
        "SHIPPED",
        "DELIVERED",
        "CANCELLED",
        "REFUNDED",
        "ON_HOLD",
      ])
      .withMessage("Invalid status"),
    query("customerId")
      .optional()
      .isString()
      .withMessage("Customer ID must be a string"),
    query("search")
      .optional()
      .isString()
      .withMessage("Search must be a string"),
    query("dateFrom")
      .optional()
      .isISO8601()
      .withMessage("Date from must be valid ISO8601 date"),
    query("dateTo")
      .optional()
      .isISO8601()
      .withMessage("Date to must be valid ISO8601 date"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const {
      page = 1,
      limit = 10,
      status,
      customerId,
      search,
      dateFrom,
      dateTo,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build where clause
    const where = {};
    if (status) where.status = status;
    if (customerId) where.customerId = customerId;
    if (search) {
      where.OR = [
        { orderNumber: { contains: search, mode: "insensitive" } },
        { customer: { email: { contains: search, mode: "insensitive" } } },
        { customer: { firstName: { contains: search, mode: "insensitive" } } },
        { customer: { lastName: { contains: search, mode: "insensitive" } } },
      ];
    }
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) where.createdAt.gte = new Date(dateFrom);
      if (dateTo) where.createdAt.lte = new Date(dateTo);
    }

    // Get orders and total count
    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        skip,
        take: parseInt(limit),
        orderBy: { [sortBy]: sortOrder },
        include: {
          customer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              customerType: true,
            },
          },
          billingAddress: true,
          shippingAddress: true,
          items: {
            include: {
              variant: {
                include: {
                  product: {
                    select: {
                      id: true,
                      name: true,
                      images: {
                        select: {
                          url: true,
                          altText: true,
                        },
                        take: 1,
                        orderBy: { sortOrder: "asc" },
                      },
                    },
                  },
                },
              },
            },
          },
          payments: {
            select: {
              id: true,
              paymentMethod: true,
              status: true,
              amount: true,
              paidAt: true,
            },
          },
          shipments: {
            select: {
              id: true,
              carrier: true,
              trackingNumber: true,
              status: true,
              shippedAt: true,
            },
          },
          _count: {
            select: {
              items: true,
              notes: true,
            },
          },
        },
      }),
      prisma.order.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit)),
        },
      },
    });
  })
);

// Get order by ID
router.get(
  "/:id",
  requirePermission("ORDERS", "READ"),
  [param("id").isString().withMessage("Order ID is required"), validateRequest],
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            mobile: true,
            customerType: true,
          },
        },
        billingAddress: true,
        shippingAddress: true,
        items: {
          include: {
            variant: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    description: true,
                    images: {
                      select: {
                        url: true,
                        altText: true,
                      },
                      take: 1,
                      orderBy: { sortOrder: "asc" },
                    },
                  },
                },
              },
            },
          },
        },
        payments: {
          include: {
            refunds: true,
          },
        },
        shipments: true,
        notes: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
        auditLogs: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
      },
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        error: "Order not found",
      });
    }

    res.json({
      success: true,
      data: order,
    });
  })
);

router.get(
  "/:id/invoice",
  requirePermission("ORDERS", "READ"),
  [param("id").isString().withMessage("Order ID is required"), validateRequest],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        customer: true,
        billingAddress: true,
        shippingAddress: true,
        items: {
          include: {
            variant: {
              include: {
                product: true,
              },
            },
          },
        },
        payments: true,
        shipments: true,
      },
    });
    if (!order) {
      return res.status(404).send("Order not found");
    }
    // Render HTML invoice
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset='utf-8'>
        <title>Invoice - ${order.orderNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          h1 { font-size: 2em; }
          .header, .footer { margin-bottom: 24px; }
          .addresses { display: flex; gap: 40px; margin-bottom: 24px; }
          .address { font-size: 0.95em; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 24px; }
          th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
          th { background: #f5f5f5; }
          .totals { float: right; width: 320px; }
          .totals td { border: none; }
        </style>
      </head>
      <body>
        <div class='header'>
          <h1>Invoice</h1>
          <div>Order #: <b>${order.orderNumber}</b></div>
          <div>Date: ${new Date(order.createdAt).toLocaleDateString()}</div>
        </div>
        <div class='addresses'>
          <div class='address'>
            <b>Billing Address</b><br>
            ${order.billingAddress?.firstName || ''} ${order.billingAddress?.lastName || ''}<br>
            ${order.billingAddress?.address1 || ''}<br>
            ${order.billingAddress?.address2 || ''}<br>
            ${order.billingAddress?.city || ''}, ${order.billingAddress?.state || ''} ${order.billingAddress?.postalCode || ''}<br>
            ${order.billingAddress?.country || ''}
          </div>
          <div class='address'>
            <b>Shipping Address</b><br>
            ${order.shippingAddress?.firstName || ''} ${order.shippingAddress?.lastName || ''}<br>
            ${order.shippingAddress?.address1 || ''}<br>
            ${order.shippingAddress?.address2 || ''}<br>
            ${order.shippingAddress?.city || ''}, ${order.shippingAddress?.state || ''} ${order.shippingAddress?.postalCode || ''}<br>
            ${order.shippingAddress?.country || ''}
          </div>
        </div>
        <div><b>Customer:</b> ${order.customer?.firstName || ''} ${order.customer?.lastName || ''} (${order.customer?.email || ''})</div>
        <table>
          <thead>
            <tr>
              <th>Product</th>
              <th>Variant</th>
              <th>SKU</th>
              <th>Qty</th>
              <th>Unit Price</th>
              <th>Total</th>
              <th>Tax Rate</th>
              <th>Tax Amount</th>
            </tr>
          </thead>
          <tbody>
            ${order.items.map(item => {
              // Use the order's tax rate for each item
              const taxRate = order.taxAmount && order.subtotal ? (order.taxAmount / order.subtotal) * 100 : 0;
              const itemTotal = Number(item.unitPrice) * item.quantity;
              const itemTaxAmount = itemTotal * (taxRate / 100);
              return `
              <tr>
                <td>${item.variant?.product?.name || ''}</td>
                <td>${item.variant?.name || ''}</td>
                <td>${item.variant?.sku || ''}</td>
                <td>${item.quantity}</td>
                <td>$${Number(item.unitPrice).toFixed(2)}</td>
                <td>$${Number(item.totalPrice).toFixed(2)}</td>
                <td>${taxRate.toFixed(2)}%</td>
                <td>$${itemTaxAmount.toFixed(2)}</td>
              </tr>
              `;
            }).join('')}
          </tbody>
        </table>
        <table class='totals'>
          <tbody>
            <tr><td>Subtotal:</td><td>$${Number(order.subtotal).toFixed(2)}</td></tr>
            <tr><td>Discount:</td><td>-$${Number(order.discountAmount).toFixed(2)}</td></tr>
            <tr><td>Shipping:</td><td>$${Number(order.shippingAmount).toFixed(2)}</td></tr>
            <tr><td>Tax:</td><td>$${Number(order.taxAmount).toFixed(2)}</td></tr>
            <tr><td><b>Grand Total:</b></td><td><b>$${Number(order.totalAmount).toFixed(2)}</b></td></tr>
          </tbody>
        </table>
        <div class='footer'>
          <b>Thank you for your business!</b>
        </div>
        <button onclick="window.print()" style="position: absolute; top: 24px; right: 24px; padding: 8px 16px; font-size: 1em;">Print</button>
      </body>
      </html>
    `;
    res.setHeader("Content-Type", "text/html");
    res.send(html);
  })
);

const getApplicableTaxRate = async (country, state) => {
  // Prefer state-specific, then country-wide, then fallback to 0
  let taxRate = null;
  if (state) {
    taxRate = await prisma.taxRate.findFirst({
      where: { country, state, isActive: true },
      orderBy: { updatedAt: "desc" },
    });
  }
  if (!taxRate) {
    taxRate = await prisma.taxRate.findFirst({
      where: { country, state: null, isActive: true },
      orderBy: { updatedAt: "desc" },
    });
  }
  return taxRate;
};

// Get applicable shipping rate based on shipping address and order details
const getApplicableShippingRate = async (countryCode, subtotal, orderWeight = 0) => {
  // Find shipping zone that includes this country
  const shippingZone = await prisma.shippingZone.findFirst({
    where: {
      countries: {
        has: countryCode // PostgreSQL array contains operator
      }
    },
    include: {
      rates: {
        where: { isActive: true },
        orderBy: { rate: 'asc' } // Get cheapest rate first
      }
    }
  });

  if (!shippingZone || !shippingZone.rates.length) {
    return null;
  }

  // Find the best applicable rate based on order criteria
  for (const rate of shippingZone.rates) {
    let isApplicable = true;

    // Check weight constraints
    if (rate.minWeight && orderWeight < parseFloat(rate.minWeight)) {
      isApplicable = false;
    }
    if (rate.maxWeight && orderWeight > parseFloat(rate.maxWeight)) {
      isApplicable = false;
    }

    // Check price constraints
    if (rate.minPrice && subtotal < parseFloat(rate.minPrice)) {
      isApplicable = false;
    }
    if (rate.maxPrice && subtotal > parseFloat(rate.maxPrice)) {
      isApplicable = false;
    }

    if (isApplicable) {
      // Check if free shipping threshold is met
      if (rate.freeShippingThreshold && subtotal >= parseFloat(rate.freeShippingThreshold)) {
        return {
          ...rate,
          finalRate: 0,
          reason: `Free shipping (order over $${rate.freeShippingThreshold})`
        };
      }

      return {
        ...rate,
        finalRate: parseFloat(rate.rate),
        reason: rate.name
      };
    }
  }

  return null;
};

// Create new order
router.post(
  "/",
  requirePermission("ORDERS", "CREATE"),
  [
    body("customerId").isString().withMessage("Customer ID is required"),
    body("billingAddressId")
      .isString()
      .withMessage("Billing address ID is required"),
    body("shippingAddressId")
      .isString()
      .withMessage("Shipping address ID is required"),
    body("items")
      .isArray({ min: 1 })
      .withMessage("At least one item is required"),
    body("items.*.variantId")
      .isString()
      .withMessage("Variant ID is required for each item"),
    body("items.*.quantity")
      .isInt({ min: 1 })
      .withMessage("Quantity must be at least 1"),
    body("items.*.unitPrice")
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Unit price must be a valid decimal"),
    body("discountAmount")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Discount amount must be a valid decimal"),
    body("shippingAmount")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Shipping amount must be a valid decimal"),
    body("taxAmount")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Tax amount must be a valid decimal"),
    body("couponCode").optional().isString().withMessage("Coupon code must be a string"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const {
      customerId,
      billingAddressId,
      shippingAddressId,
      items,
      discountAmount = 0,
      shippingAmount = 0,
      taxAmount = 0,
      couponCode,
    } = req.body;

    // Verify customer exists and get their type
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      select: {
        id: true,
        customerType: true,
      },
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        error: "Customer not found",
      });
    }

    // Verify addresses exist and belong to customer
    const [billingAddress, shippingAddress] = await Promise.all([
      prisma.address.findFirst({
        where: {
          id: billingAddressId,
          customerId
        },
      }),
      prisma.address.findFirst({
        where: {
          id: shippingAddressId,
          customerId
        },
      }),
    ]);

    if (!billingAddress) {
      return res.status(404).json({
        success: false,
        error: `Billing address not found or doesn't belong to customer`,
      });
    }

    if (!shippingAddress) {
      return res.status(404).json({
        success: false,
        error: `Shipping address not found or doesn't belong to customer`,
      });
    }

    // Fetch applicable tax rate and shipping rate based on shipping address
    let taxRateValue = 0;
    let taxRateType = null;
    let autoShippingAmount = 0;
    let shippingRateInfo = null;

    if (shippingAddress) {
      // Convert country/state names to ISO codes (same logic as frontend)
      const { countryCode, stateCode } = getCountryStateIsoCodes(shippingAddress.country, shippingAddress.state);

      if (countryCode) {
        // Get tax rate
        const taxRate = await getApplicableTaxRate(countryCode, stateCode);
        if (taxRate) {
          taxRateValue = parseFloat(taxRate.rate);
          taxRateType = taxRate.type;
        }
        // Shipping rate will be calculated after subtotal is available
      }
    }

    // Verify all variants exist and calculate totals
    const variants = await prisma.productVariant.findMany({
      where: {
        id: {
          in: items.map((item) => item.variantId),
        },
      },
      include: {
        inventory: {
          select: {
            id: true,
            locationId: true,
            quantity: true,
            reservedQty: true,
          },
        },
        segmentPrices: {
          where: {
            customerType: customer.customerType,
          },
        },
      },
    });

    if (variants.length !== items.length) {
      const foundVariantIds = variants.map(v => v.id);
      const missingVariantIds = items
        .map(item => item.variantId)
        .filter(id => !foundVariantIds.includes(id));

      return res.status(404).json({
        success: false,
        error: `Product variants not found: ${missingVariantIds.join(', ')}`,
      });
    }

    // Verify user exists (for userId foreign key)
    if (req.user?.id) {
      const userExists = await prisma.user.findUnique({
        where: { id: req.user.id },
        select: { id: true }
      });

      if (!userExists) {
        return res.status(404).json({
          success: false,
          error: "User not found",
        });
      }
    }

    // Check inventory availability and get correct prices
    const itemsWithPrices = items.map((item) => {
      const variant = variants.find((v) => v.id === item.variantId);
      const totalInventory = variant.inventory.reduce(
        (sum, inv) =>
          sum + Math.max(0, (inv.quantity || 0) - (inv.reservedQty || 0)),
        0
      );

      // Get segment-specific price if available, otherwise use default price
      const segmentPrice = variant.segmentPrices[0];
      const unitPrice = segmentPrice
        ? segmentPrice.salePrice || segmentPrice.regularPrice
        : variant.salePrice || variant.regularPrice;

      return {
        ...item,
        unitPrice: parseFloat(unitPrice),
        totalInventory,
        sufficient: totalInventory >= item.quantity,
      };
    });

    const insufficientItems = itemsWithPrices.filter(
      (item) => !item.sufficient
    );
    if (insufficientItems.length > 0) {
      return res.status(400).json({
        success: false,
        error: "Insufficient inventory",
        details: insufficientItems,
      });
    }

    // Calculate totals using segment-specific prices
    const subtotal = itemsWithPrices.reduce(
      (sum, item) => sum + item.unitPrice * item.quantity,
      0
    );

    // Now calculate shipping rate using the subtotal
    if (shippingAddress) {
      const { countryCode } = getCountryStateIsoCodes(shippingAddress.country, shippingAddress.state);
      if (countryCode) {
        // Get shipping rate (calculate order weight if needed)
        const orderWeight = 0; // TODO: Calculate actual weight from products
        const applicableShippingRate = await getApplicableShippingRate(countryCode, subtotal, orderWeight);

        if (applicableShippingRate) {
          autoShippingAmount = applicableShippingRate.finalRate;
          shippingRateInfo = {
            name: applicableShippingRate.name,
            reason: applicableShippingRate.reason,
            zoneId: applicableShippingRate.zoneId
          };
        }
      }
    }

    let finalDiscountAmount = Math.round((parseFloat(discountAmount) || 0) * 100) / 100;
    let appliedCoupon = null;
    if (couponCode) {
      // Validate coupon
      const now = new Date();
      const promo = await prisma.promotion.findFirst({
        where: {
          code: couponCode.toUpperCase(),
          isActive: true,
          OR: [
            { startsAt: null },
            { startsAt: { lte: now } }
          ],
          AND: [
            {
              OR: [
                { expiresAt: null },
                { expiresAt: { gte: now } }
              ]
            }
          ]
        },
        include: {
          productRules: {
            include: {
              product: true,
              variant: true
            }
          },
          categoryRules: {
            include: {
              category: true
            }
          },
          volumeTiers: {
            orderBy: { minQuantity: 'asc' }
          }
        }
      });

      // Additional check for usage limit
      if (promo && promo.usageLimit && promo.usageCount >= promo.usageLimit) {
        return res.status(400).json({ success: false, error: 'Coupon usage limit exceeded' });
      }

      if (!promo) {
        return res.status(400).json({ success: false, error: "Invalid or expired coupon code" });
      }

      // Get customer details for eligibility check
      const customerDetails = await prisma.customer.findUnique({
        where: { id: customerId },
        select: { customerType: true }
      });

      // Prepare order items with variant details for calculation
      const orderItemsWithVariants = await Promise.all(
        items.map(async (item) => {
          const variant = await prisma.productVariant.findUnique({
            where: { id: item.variantId },
            include: { product: true }
          });
          return {
            ...item,
            variant,
            unitPrice: item.unitPrice
          };
        })
      );

      // Calculate discount using advanced promotion calculator
      const discountResult = await calculatePromotionDiscount(
        promo,
        orderItemsWithVariants,
        customerDetails,
        subtotal,
        parseFloat(shippingAmount)
      );

      if (discountResult.error) {
        return res.status(400).json({ success: false, error: discountResult.error });
      }

      finalDiscountAmount += Math.round(discountResult.discount * 100) / 100;
      appliedCoupon = promo;

      // Increment usage count
      await prisma.promotion.update({
        where: { code: couponCode.toUpperCase() },
        data: { usageCount: { increment: 1 } },
      });
    }

    // Use auto-calculated shipping if no manual shipping amount provided
    const finalShippingAmount = parseFloat(shippingAmount) || autoShippingAmount;
    const taxableAmount = subtotal - finalDiscountAmount + finalShippingAmount;
    const finalTaxAmount = Math.round(taxableAmount * (taxRateValue / 100) * 100) / 100;
    const totalAmount = Math.round((subtotal - finalDiscountAmount + finalShippingAmount + finalTaxAmount) * 100) / 100;

    // Create order in transaction
    const order = await prisma.$transaction(async (tx) => {
      // Generate order number
      const orderNumber = generateOrderNumber();

      // Create order
      let newOrder;
      try {
        newOrder = await tx.order.create({
          data: {
            orderNumber,
            customerId,
            userId: req.user?.id || null,
            status: "PENDING",
            subtotal,
            discountAmount: finalDiscountAmount,
            shippingAmount: finalShippingAmount,
            taxAmount: finalTaxAmount,
            totalAmount,
            billingAddressId,
            shippingAddressId,
          },
        });
      } catch (error) {
        console.error('Error creating order:', error);
        if (error.code === 'P2003') {
          throw new Error(`Foreign key constraint failed: ${error.meta?.field_name || 'unknown field'}`);
        }
        throw error;
      }

      // Create order items with segment-specific prices
      const orderItems = itemsWithPrices.map((item) => ({
        orderId: newOrder.id,
        variantId: item.variantId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.unitPrice * item.quantity,
      }));

      await tx.orderItem.createMany({
        data: orderItems,
      });

      // Reserve inventory
      for (const item of itemsWithPrices) {
        const variant = variants.find((v) => v.id === item.variantId);
        const inventoryRecords = variant.inventory;

        let remainingQty = item.quantity;
        for (const inventory of inventoryRecords) {
          if (remainingQty <= 0) break;

          const availableQty = Math.max(
            0,
            (inventory.quantity || 0) - (inventory.reservedQty || 0)
          );
          const reserveQty = Math.min(remainingQty, availableQty);

          if (reserveQty > 0) {
            await tx.inventory.update({
              where: {
                variantId_locationId: {
                  variantId: variant.id,
                  locationId: inventory.locationId,
                },
              },
              data: {
                reservedQty: { increment: reserveQty },
              },
            });
            remainingQty -= reserveQty;
          }
        }
      }

      return newOrder;
    });

    // Create audit log after transaction is complete
    try {
      await logOrderAudit({
        orderId: order.id,
        userId: req.user?.id,
        action: "ORDER_CREATED",
        details: {
          orderNumber: order.orderNumber,
          totalAmount,
          itemCount: items.length,
          customerType: customer.customerType
        },
        req
      });
    } catch (auditError) {
      console.error('Failed to create audit log:', auditError);
      // Don't fail the order creation if audit log fails
    }

    // Fetch complete order with relations
    const completeOrder = await prisma.order.findUnique({
      where: { id: order.id },
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            customerType: true,
          },
        },
        billingAddress: true,
        shippingAddress: true,
        items: {
          include: {
            variant: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    images: {
                      select: {
                        url: true,
                        altText: true,
                      },
                      take: 1,
                      orderBy: { sortOrder: "asc" },
                    },
                  },
                },
                segmentPrices: {
                  where: {
                    customerType: customer.customerType,
                  },
                },
              },
            },
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      message: "Order created successfully",
      data: { ...completeOrder, appliedCoupon },
    });
  })
);

// Update order
router.put(
  "/:id",
  requirePermission("ORDERS", "UPDATE"),
  [
    param("id").isString().withMessage("Order ID is required"),
    body("status")
      .optional()
      .isIn([
        "PENDING",
        "PROCESSING",
        "SHIPPED",
        "DELIVERED",
        "CANCELLED",
        "REFUNDED",
        "ON_HOLD",
      ])
      .withMessage("Invalid status"),
    body("billingAddressId")
      .optional()
      .isString()
      .withMessage("Billing address ID must be a string"),
    body("shippingAddressId")
      .optional()
      .isString()
      .withMessage("Shipping address ID must be a string"),
    body("discountAmount")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Discount amount must be a valid decimal"),
    body("shippingAmount")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Shipping amount must be a valid decimal"),
    body("taxAmount")
      .optional()
      .isDecimal({ decimal_digits: "0,2" })
      .withMessage("Tax amount must be a valid decimal"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const {
      status,
      billingAddressId,
      shippingAddressId,
      discountAmount,
      shippingAmount,
      taxAmount,
    } = req.body;

    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
      include: {
        items: true,
      },
    });

    if (!existingOrder) {
      return res.status(404).json({
        success: false,
        error: "Order not found",
      });
    }

    // Prepare update data
    const updateData = {};
    if (status) updateData.status = status;
    if (billingAddressId) updateData.billingAddressId = billingAddressId;
    if (shippingAddressId) updateData.shippingAddressId = shippingAddressId;
    if (discountAmount !== undefined)
      updateData.discountAmount = parseFloat(discountAmount);
    if (shippingAmount !== undefined)
      updateData.shippingAmount = parseFloat(shippingAmount);
    if (taxAmount !== undefined) updateData.taxAmount = parseFloat(taxAmount);

    // Recalculate total if amounts changed
    if (
      discountAmount !== undefined ||
      shippingAmount !== undefined ||
      taxAmount !== undefined
    ) {
      const subtotal = existingOrder.subtotal;
      const finalDiscountAmount =
        discountAmount !== undefined
          ? parseFloat(discountAmount)
          : existingOrder.discountAmount;
      const finalShippingAmount =
        shippingAmount !== undefined
          ? parseFloat(shippingAmount)
          : existingOrder.shippingAmount;
      const finalTaxAmount =
        taxAmount !== undefined
          ? parseFloat(taxAmount)
          : existingOrder.taxAmount;

      updateData.totalAmount =
        subtotal - finalDiscountAmount + finalShippingAmount + finalTaxAmount;
    }

    // Update order in transaction
    const order = await prisma.$transaction(async (tx) => {
      const updatedOrder = await tx.order.update({
        where: { id },
        data: updateData,
      });

      // Create audit log
      await logOrderAudit({ orderId: id, userId: req.user.id, action: "ORDER_UPDATED", details: { changes: updateData, previousStatus: existingOrder.status, newStatus: status || existingOrder.status }, req });

      return updatedOrder;
    });

    // Fetch complete order with relations
    const completeOrder = await prisma.order.findUnique({
      where: { id: order.id },
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            customerType: true,
          },
        },
        billingAddress: true,
        shippingAddress: true,
        items: {
          include: {
            variant: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    images: {
                      select: {
                        url: true,
                        altText: true,
                      },
                      take: 1,
                      orderBy: { sortOrder: "asc" },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    res.json({
      success: true,
      message: "Order updated successfully",
      data: completeOrder,
    });
  })
);

// Update order status
router.patch(
  "/:id/status",
  requirePermission("ORDERS", "UPDATE"),
  [
    param("id").isString().withMessage("Order ID is required"),
    body("status")
      .isIn([
        "PENDING",
        "PROCESSING",
        "SHIPPED",
        "DELIVERED",
        "CANCELLED",
        "REFUNDED",
        "ON_HOLD",
      ])
      .withMessage("Invalid status"),
    body("note").optional().isString().withMessage("Note must be a string"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { status, note } = req.body;

    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
    });

    if (!existingOrder) {
      return res.status(404).json({
        success: false,
        error: "Order not found",
      });
    }

    // Update order status in transaction
    const order = await prisma.$transaction(async (tx) => {
      const updatedOrder = await tx.order.update({
        where: { id },
        data: { status },
      });

      // Add note if provided
      if (note) {
        await tx.orderNote.create({
          data: {
            orderId: id,
            userId: req.user.id,
            note,
            isInternal: true,
          },
        });
      }

      // Create audit log
      await logOrderAudit({ orderId: id, userId: req.user.id, action: "STATUS_UPDATED", details: { previousStatus: existingOrder.status, newStatus: status, note: note || null }, req });

      return updatedOrder;
    });

    res.json({
      success: true,
      message: "Order status updated successfully",
      data: {
        orderId: order.id,
        orderNumber: order.orderNumber,
        status: order.status,
        updatedAt: order.updatedAt,
      },
    });
  })
);

// Add note to order
router.post(
  "/:id/notes",
  requirePermission("ORDERS", "UPDATE"),
  [
    param("id").isString().withMessage("Order ID is required"),
    body("note").notEmpty().withMessage("Note is required"),
    body("isInternal")
      .optional()
      .isBoolean()
      .withMessage("isInternal must be boolean"),
    validateRequest,
  ],
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { note, isInternal = true } = req.body;

    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
    });

    if (!existingOrder) {
      return res.status(404).json({
        success: false,
        error: "Order not found",
      });
    }

    // Create note
    const orderNote = await prisma.orderNote.create({
      data: {
        orderId: id,
        userId: req.user.id,
        note,
        isInternal,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      message: "Note added successfully",
      data: orderNote,
    });
  })
);

// Get order notes
router.get(
  "/:id/notes",
  requirePermission("ORDERS", "READ"),
  [param("id").isString().withMessage("Order ID is required"), validateRequest],
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
    });

    if (!existingOrder) {
      return res.status(404).json({
        success: false,
        error: "Order not found",
      });
    }

    // Get notes
    const notes = await prisma.orderNote.findMany({
      where: { orderId: id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    res.json({
      success: true,
      data: notes,
    });
  })
);

// Delete order (soft delete)
router.delete(
  "/:id",
  requirePermission("ORDERS", "DELETE"),
  [param("id").isString().withMessage("Order ID is required"), validateRequest],
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
    });

    if (!existingOrder) {
      return res.status(404).json({
        success: false,
        error: "Order not found",
      });
    }

    // Cannot delete shipped or delivered orders
    if (["SHIPPED", "DELIVERED"].includes(existingOrder.status)) {
      return res.status(400).json({
        success: false,
        error: "Cannot delete shipped or delivered orders",
      });
    }

    // Cancel order instead of deleting
    await prisma.order.update({
      where: { id },
      data: { status: "CANCELLED" },
    });

    // Create audit log
    await logOrderAudit({ orderId: id, userId: req.user.id, action: "ORDER_CANCELLED", details: { reason: "Order deleted by admin", previousStatus: existingOrder.status }, req });

    res.json({
      success: true,
      message: "Order cancelled successfully",
    });
  })
);

// Bulk delete (cancel) orders
router.post(
  "/bulk-delete",
  requirePermission("ORDERS", "DELETE"),
  [body("ids").isArray({ min: 1 }).withMessage("ids must be a non-empty array"), validateRequest],
  asyncHandler(async (req, res) => {
    const { ids } = req.body;
    // Only cancel orders that are not shipped or delivered
    const result = await prisma.order.updateMany({
      where: { id: { in: ids }, status: { notIn: ["SHIPPED", "DELIVERED"] } },
      data: { status: "CANCELLED" },
    });
    res.json({ success: true, cancelled: result.count });
  })
);

// Bulk import orders
router.post(
  "/bulk-import",
  requirePermission("ORDERS", "CREATE"),
  [body("orders").isArray({ min: 1 }).withMessage("orders must be a non-empty array"), validateRequest],
  asyncHandler(async (req, res) => {
    const { orders } = req.body;
    let created = 0;
    await prisma.$transaction(async (tx) => {
      for (const o of orders) {
        if (!o.customerId || !Array.isArray(o.items) || o.items.length === 0) continue;
        const items = o.items.filter(i => i.variantId && i.quantity);
        if (items.length === 0) continue;
        await tx.order.create({
          data: {
            customerId: o.customerId,
            billingAddressId: o.billingAddressId,
            shippingAddressId: o.shippingAddressId,
            status: o.status || 'PENDING',
            items: {
              create: items.map(i => ({
                variantId: i.variantId,
                quantity: i.quantity,
                unitPrice: i.unitPrice || 0,
              })),
            },
          },
        });
        created++;
      }
    });
    res.json({ success: true, created });
  })
);

// Customer-initiated cancellation request
router.post(
  '/:id/request-cancellation',
  authMiddleware,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    // Only the customer who owns the order can request cancellation
    const order = await prisma.order.findUnique({ where: { id } });
    if (!order) {
      return res.status(404).json({ success: false, error: 'Order not found' });
    }
    if (order.customerId !== req.user.id && req.user.role !== 'ADMIN') {
      return res.status(403).json({ success: false, error: 'Not authorized to cancel this order' });
    }
    // Only allow cancellation if order is not already cancelled/refunded/delivered
    if (["CANCELLED", "REFUNDED", "DELIVERED"].includes(order.status)) {
      return res.status(400).json({ success: false, error: 'Order cannot be cancelled at this stage' });
    }
    // Mark order as having a cancellation request
    const updatedOrder = await prisma.order.update({
      where: { id },
      data: { status: 'ON_HOLD' }, // or add a cancellationRequest: true field if you want
    });
    // Optionally, add an order note
    await prisma.orderNote.create({
      data: {
        orderId: id,
        userId: req.user.id,
        note: 'Customer requested cancellation',
        isInternal: false,
      },
    });
    res.json({ success: true, data: updatedOrder });
  })
);

// Initiate a refund for an order (admin only)
router.post(
  '/:id/refund',
  requireRole(['ADMIN']),
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { amount, reason } = req.body;
    // Find the order and payment
    const order = await prisma.order.findUnique({ where: { id }, include: { payments: true } });
    if (!order) {
      return res.status(404).json({ success: false, error: 'Order not found' });
    }
    const payment = order.payments[0];
    if (!payment) {
      return res.status(400).json({ success: false, error: 'No payment found for this order' });
    }
    // Create refund record
    const refund = await prisma.refund.create({
      data: {
        paymentId: payment.id,
        amount,
        reason,
        status: 'PENDING',
      },
    });
    // Log audit
    await logOrderAudit({
      orderId: id,
      userId: req.user.id,
      action: 'REFUND_INITIATED',
      details: { amount, reason, refundId: refund.id },
      req,
    });
    res.json({ success: true, data: refund });
  })
);
// Update refund status (admin only)
router.put(
  '/refunds/:refundId/status',
  requireRole(['ADMIN']),
  asyncHandler(async (req, res) => {
    const { refundId } = req.params;
    const { status } = req.body;
    const refund = await prisma.refund.update({
      where: { id: refundId },
      data: { status },
    });
    // Log audit
    await logOrderAudit({
      orderId: refund.payment.orderId,
      userId: req.user.id,
      action: 'REFUND_STATUS_UPDATE',
      details: { refundId, newStatus: status },
      req,
    });
    res.json({ success: true, data: refund });
  })
);

// Add audit log utility
async function logOrderAudit({ orderId, userId, action, details, req }) {
  await prisma.auditLog.create({
    data: {
      orderId,
      userId: userId || null,
      action,
      details,
      ipAddress: req?.ip || null,
      userAgent: req?.headers['user-agent'] || null,
    },
  });
}

module.exports = router;
