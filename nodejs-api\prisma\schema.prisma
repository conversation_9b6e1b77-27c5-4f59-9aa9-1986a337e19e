// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management & RBAC
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String
  lastName  String
  role      UserRole @default(STAFF)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  orders      Order[]
  orderNotes  OrderNote[]
  auditLogs   AuditLog[]
  permissions UserPermission[]

  @@map("users")
}

model UserPermission {
  id        String   @id @default(cuid())
  userId    String
  module    String
  action    String // CREATE, READ, UPDATE, DELETE
  granted   <PERSON><PERSON><PERSON>  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, module, action])
  @@map("user_permissions")
}

// Customer Management
model Customer {
  id           String       @id @default(cuid())
  firstName    String
  middleName   String?
  lastName     String
  email        String       @unique
  mobile       String       @unique
  customerType CustomerType @default(B2C)
  isActive     Boolean      @default(true)
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  // Relations
  addresses      Address[]
  orders         Order[]
  reviews        ProductReview[]
  customerTags   CustomerTag[]
  promotionUsage PromotionUsage[]

  @@map("customers")
}

model Address {
  id         String      @id @default(cuid())
  customerId String
  type       AddressType
  firstName  String
  lastName   String
  company    String?
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String      @default("US")
  phone      String?
  isDefault  Boolean     @default(false)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  // Relations
  customer       Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  billingOrders  Order[]  @relation("BillingAddress")
  shippingOrders Order[]  @relation("ShippingAddress")

  @@map("addresses")
}

model CustomerTag {
  id         String   @id @default(cuid())
  customerId String
  tag        String
  createdAt  DateTime @default(now())

  // Relations
  customer Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@unique([customerId, tag])
  @@map("customer_tags")
}

// Product Management
model Product {
  id          String        @id @default(cuid())
  name        String
  description String?
  status      ProductStatus @default(DRAFT)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  variants          ProductVariant[]
  images            ProductImage[]
  categories        ProductCategory[]
  tags              ProductTag[]
  relatedProducts   ProductRelation[]      @relation("MainProduct")
  relatedToProducts ProductRelation[]      @relation("RelatedProduct")
  reviews           ProductReview[]
  collections       ProductCollection[]
  promotionRules    PromotionProductRule[]

  @@map("products")
}

model ProductVariant {
  id           String   @id @default(cuid())
  productId    String
  sku          String   @unique
  name         String
  description  String?
  regularPrice Decimal  @db.Decimal(10, 2)
  salePrice    Decimal? @db.Decimal(10, 2)
  hsn          String?
  weight       Decimal? @db.Decimal(8, 2)
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // SEO
  seoTitle       String?
  seoDescription String?
  seoSlug        String? @unique

  // Relations
  product        Product                @relation(fields: [productId], references: [id], onDelete: Cascade)
  variantOptions VariantOption[]
  inventory      Inventory[]
  orderItems     OrderItem[]
  segmentPrices  SegmentPrice[]
  promotionRules PromotionProductRule[]

  @@map("product_variants")
}

model SegmentPrice {
  id           String       @id @default(cuid())
  variantId    String
  customerType CustomerType
  regularPrice Decimal      @db.Decimal(10, 2)
  salePrice    Decimal?     @db.Decimal(10, 2)
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  // Relations
  variant ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@unique([variantId, customerType])
  @@map("segment_prices")
}

model VariantOption {
  id        String   @id @default(cuid())
  variantId String
  name      String // Size, Color, Material
  value     String // Large, Red, Cotton
  createdAt DateTime @default(now())

  // Relations
  variant ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("variant_options")
}

model ProductImage {
  id        String   @id @default(cuid())
  productId String
  url       String
  altText   String?
  sortOrder Int      @default(0)
  createdAt DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductCategory {
  id        String   @id @default(cuid())
  productId String
  name      String
  createdAt DateTime @default(now())

  // Relations
  product        Product                 @relation(fields: [productId], references: [id], onDelete: Cascade)
  promotionRules PromotionCategoryRule[]

  @@map("product_categories")
}

model ProductTag {
  id        String   @id @default(cuid())
  productId String
  tag       String
  createdAt DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, tag])
  @@map("product_tags")
}

model ProductRelation {
  id               String       @id @default(cuid())
  productId        String
  relatedProductId String
  type             RelationType
  createdAt        DateTime     @default(now())

  // Relations
  product        Product @relation("MainProduct", fields: [productId], references: [id], onDelete: Cascade)
  relatedProduct Product @relation("RelatedProduct", fields: [relatedProductId], references: [id], onDelete: Cascade)

  @@unique([productId, relatedProductId, type])
  @@map("product_relations")
}

model ProductReview {
  id         String   @id @default(cuid())
  productId  String
  customerId String
  rating     Int // 1-5
  title      String?
  comment    String?
  isApproved Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  product  Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  customer Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@unique([productId, customerId])
  @@map("product_reviews")
}

// Inventory Management
model Inventory {
  id            String   @id @default(cuid())
  variantId     String
  locationId    String
  quantity      Int      @default(0)
  reservedQty   Int      @default(0)
  lowStockAlert Int      @default(10)
  updatedAt     DateTime @updatedAt

  // Relations
  variant   ProductVariant      @relation(fields: [variantId], references: [id], onDelete: Cascade)
  location  Location            @relation(fields: [locationId], references: [id], onDelete: Cascade)
  movements InventoryMovement[]
  batches   InventoryBatch[]

  @@unique([variantId, locationId])
  @@map("inventory")
}

model InventoryBatch {
  id          String    @id @default(cuid())
  inventoryId String
  batchNumber String
  quantity    Int       @default(0)
  expiryDate  DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  inventory Inventory @relation(fields: [inventoryId], references: [id], onDelete: Cascade)

  @@unique([inventoryId, batchNumber])
  @@map("inventory_batches")
}

model Location {
  id        String   @id @default(cuid())
  name      String   @unique
  address   String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  inventory Inventory[]

  @@map("locations")
}

model InventoryMovement {
  id          String       @id @default(cuid())
  inventoryId String
  type        MovementType
  quantity    Int
  reason      String?
  batchNumber String?
  createdAt   DateTime     @default(now())

  // Relations
  inventory Inventory @relation(fields: [inventoryId], references: [id], onDelete: Cascade)

  @@map("inventory_movements")
}

// Order Management
model Order {
  id                String      @id @default(cuid())
  orderNumber       String      @unique
  customerId        String
  userId            String?
  status            OrderStatus @default(PENDING)
  subtotal          Decimal     @db.Decimal(10, 2)
  discountAmount    Decimal     @default(0) @db.Decimal(10, 2)
  shippingAmount    Decimal     @default(0) @db.Decimal(10, 2)
  taxAmount         Decimal     @default(0) @db.Decimal(10, 2)
  totalAmount       Decimal     @db.Decimal(10, 2)
  billingAddressId  String
  shippingAddressId String
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  // Relations
  customer        Customer         @relation(fields: [customerId], references: [id])
  user            User?            @relation(fields: [userId], references: [id])
  billingAddress  Address          @relation("BillingAddress", fields: [billingAddressId], references: [id])
  shippingAddress Address          @relation("ShippingAddress", fields: [shippingAddressId], references: [id])
  items           OrderItem[]
  payments        Payment[]
  shipments       Shipment[]
  notes           OrderNote[]
  auditLogs       AuditLog[]
  transactions    Transaction[]
  promotionUsage  PromotionUsage[]

  @@map("orders")
}

model OrderItem {
  id         String   @id @default(cuid())
  orderId    String
  variantId  String
  quantity   Int
  unitPrice  Decimal  @db.Decimal(10, 2)
  totalPrice Decimal  @db.Decimal(10, 2)
  createdAt  DateTime @default(now())

  // Relations
  order   Order          @relation(fields: [orderId], references: [id], onDelete: Cascade)
  variant ProductVariant @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

model OrderNote {
  id         String   @id @default(cuid())
  orderId    String
  userId     String
  note       String
  isInternal Boolean  @default(true)
  createdAt  DateTime @default(now())

  // Relations
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)
  user  User  @relation(fields: [userId], references: [id])

  @@map("order_notes")
}

// Payment Management
model Payment {
  id            String        @id @default(cuid())
  orderId       String
  paymentMethod PaymentMethod
  provider      String // stripe, paypal, etc.
  transactionId String?
  amount        Decimal       @db.Decimal(10, 2)
  currency      String        @default("USD")
  status        PaymentStatus @default(PENDING)
  paidAt        DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  order   Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  refunds Refund[]

  @@map("payments")
}

model Refund {
  id         String       @id @default(cuid())
  paymentId  String
  amount     Decimal      @db.Decimal(10, 2)
  reason     String?
  status     RefundStatus @default(PENDING)
  refundedAt DateTime?
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt

  // Relations
  payment Payment @relation(fields: [paymentId], references: [id], onDelete: Cascade)

  @@map("refunds")
}

// Shipping Management
model Shipment {
  id             String         @id @default(cuid())
  orderId        String
  carrier        String
  trackingNumber String?
  trackingUrl    String?
  status         ShipmentStatus @default(PENDING)
  shippedAt      DateTime?
  deliveredAt    DateTime?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("shipments")
}

model ShippingZone {
  id        String   @id @default(cuid())
  name      String   @unique
  countries String[] // Array of country codes
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  rates ShippingRate[]

  @@map("shipping_zones")
}

model ShippingRate {
  id                    String   @id @default(cuid())
  zoneId                String
  name                  String
  minWeight             Decimal? @db.Decimal(8, 2)
  maxWeight             Decimal? @db.Decimal(8, 2)
  minPrice              Decimal? @db.Decimal(10, 2)
  maxPrice              Decimal? @db.Decimal(10, 2)
  rate                  Decimal  @db.Decimal(10, 2)
  freeShippingThreshold Decimal? @db.Decimal(10, 2)
  estimatedDays         String?  // e.g., "3-5", "1-2"
  isActive              Boolean  @default(true)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  zone ShippingZone @relation(fields: [zoneId], references: [id], onDelete: Cascade)

  @@map("shipping_rates")
}

model Carrier {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique // e.g., "FEDEX", "UPS", "USPS"
  apiKey      String?
  apiSecret   String?
  isActive    Boolean  @default(true)
  services    String[] // Available services like ["Ground", "Express", "Overnight"]
  trackingUrl String?  // Template URL for tracking, e.g., "https://fedex.com/track?id={trackingNumber}"
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("carriers")
}

// Promotions & Discounts
model Promotion {
  id             String        @id @default(cuid())
  code           String        @unique
  name           String
  description    String?
  type           PromotionType
  value          Decimal       @db.Decimal(10, 2)
  minOrderAmount Decimal?      @db.Decimal(10, 2)
  maxDiscount    Decimal?      @db.Decimal(10, 2)
  usageLimit     Int?
  usageCount     Int           @default(0)
  isActive       Boolean       @default(true)
  startsAt       DateTime?
  expiresAt      DateTime?

  // Customer segmentation
  customerTypes CustomerType[] // Which customer types can use this promotion

  // BOGO specific fields
  bogoType    BogoType? // Type of BOGO offer
  buyQuantity Int? // Quantity to buy (for BOGO)
  getQuantity Int? // Quantity to get free/discounted (for BOGO)
  getDiscount Decimal?  @db.Decimal(5, 2) // Discount % for "get" items (for BOGO)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  productRules  PromotionProductRule[]
  categoryRules PromotionCategoryRule[]
  volumeTiers   PromotionVolumeTier[]
  usageHistory  PromotionUsage[]

  @@map("promotions")
}

// BOGO and Product-specific promotion rules
model PromotionProductRule {
  id          String   @id @default(cuid())
  promotionId String
  productId   String? // Specific product (optional)
  variantId   String? // Specific variant (optional)
  ruleType    RuleType // BUY, GET, EXCLUDE
  quantity    Int? // Required quantity for this rule
  createdAt   DateTime @default(now())

  // Relations
  promotion Promotion       @relation(fields: [promotionId], references: [id], onDelete: Cascade)
  product   Product?        @relation(fields: [productId], references: [id])
  variant   ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("promotion_product_rules")
}

// Category-specific promotion rules
model PromotionCategoryRule {
  id          String   @id @default(cuid())
  promotionId String
  categoryId  String
  ruleType    RuleType // INCLUDE, EXCLUDE
  createdAt   DateTime @default(now())

  // Relations
  promotion Promotion       @relation(fields: [promotionId], references: [id], onDelete: Cascade)
  category  ProductCategory @relation(fields: [categoryId], references: [id])

  @@map("promotion_category_rules")
}

// Volume discount tiers
model PromotionVolumeTier {
  id            String           @id @default(cuid())
  promotionId   String
  minQuantity   Int // Minimum quantity for this tier
  maxQuantity   Int? // Maximum quantity for this tier (optional)
  discountType  TierDiscountType // PERCENTAGE, FIXED_AMOUNT, FIXED_PRICE
  discountValue Decimal          @db.Decimal(10, 2)
  createdAt     DateTime         @default(now())

  // Relations
  promotion Promotion @relation(fields: [promotionId], references: [id], onDelete: Cascade)

  @@map("promotion_volume_tiers")
}

// Promotion usage tracking
model PromotionUsage {
  id             String   @id @default(cuid())
  promotionId    String
  orderId        String
  customerId     String
  discountAmount Decimal  @db.Decimal(10, 2)
  usedAt         DateTime @default(now())

  // Relations
  promotion Promotion @relation(fields: [promotionId], references: [id])
  order     Order     @relation(fields: [orderId], references: [id])
  customer  Customer  @relation(fields: [customerId], references: [id])

  @@map("promotion_usage")
}

// System Configuration
model Setting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  type      String   @default("string")
  category  String   @default("general")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

// Audit Log
model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  orderId   String?
  action    String
  details   Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relations
  user  User   @relation(fields: [userId], references: [id])
  order Order? @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("audit_logs")
}

// Collections Management
model Collection {
  id          String   @id @default(cuid())
  name        String
  description String?
  slug        String   @unique
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  products ProductCollection[]

  @@map("collections")
}

model ProductCollection {
  id           String   @id @default(cuid())
  collectionId String
  productId    String
  sortOrder    Int      @default(0)
  createdAt    DateTime @default(now())

  // Relations
  collection Collection @relation(fields: [collectionId], references: [id], onDelete: Cascade)
  product    Product    @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([collectionId, productId])
  @@map("product_collections")
}

// Enums
enum UserRole {
  ADMIN
  MANAGER
  STAFF
}

enum CustomerType {
  B2C
  B2B
  ENTERPRISE
}

enum AddressType {
  BILLING
  SHIPPING
}

enum ProductStatus {
  DRAFT
  ACTIVE
  INACTIVE
  ARCHIVED
}

enum RelationType {
  RELATED
  UPSELL
  CROSS_SELL
}

enum OrderStatus {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
  ON_HOLD
}

enum PaymentMethod {
  CREDIT_CARD
  DEBIT_CARD
  PAYPAL
  STRIPE
  BANK_TRANSFER
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum RefundStatus {
  PENDING
  APPROVED
  PROCESSED
  REJECTED
}

enum ShipmentStatus {
  PENDING
  SHIPPED
  IN_TRANSIT
  DELIVERED
  RETURNED
  CANCELLED
}

enum MovementType {
  INBOUND
  OUTBOUND
  ADJUSTMENT
  TRANSFER
}

enum PromotionType {
  PERCENTAGE
  FIXED_AMOUNT
  FREE_SHIPPING
  BOGO
  VOLUME_DISCOUNT
}

enum BogoType {
  BUY_X_GET_Y_FREE // Buy X, Get Y Free
  BUY_X_GET_Y_PERCENT // Buy X, Get Y at % discount
  BUY_X_GET_Y_FIXED // Buy X, Get Y at fixed price
  CHEAPEST_FREE // Buy X, get cheapest free
  MOST_EXPENSIVE_FREE // Buy X, get most expensive free
}

enum RuleType {
  BUY // Products that must be bought
  GET // Products that are given free/discounted
  INCLUDE // Products/categories included in promotion
  EXCLUDE // Products/categories excluded from promotion
}

enum TierDiscountType {
  PERCENTAGE // Percentage discount
  FIXED_AMOUNT // Fixed amount off
  FIXED_PRICE // Fixed price per unit
}

model TaxRate {
  id        String   @id @default(cuid())
  country   String // e.g., "US"
  state     String? // e.g., "CA"
  rate      Decimal  @db.Decimal(5, 2)
  type      String // e.g., "State Tax", "VAT"
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Transaction {
  id                          String             @id @default(cuid())
  paymentGatewayTransactionId String? // nullable
  amount                      Decimal            @db.Decimal(10, 2)
  orderId                     String
  paymentStatus               PaymentStatus
  paymentGatewayName          PaymentGatewayName
  paymentGatewayResponse      String? // nullable, can be JSON string
  createdAt                   DateTime           @default(now())
  updatedAt                   DateTime           @updatedAt

  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

enum PaymentGatewayName {
  DIRECT
  STRIPE
  PAYPAL
  MANUAL
  OTHER
}
