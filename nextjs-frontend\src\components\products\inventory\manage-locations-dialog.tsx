import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { api } from '@/lib/api';
import { toast } from 'sonner';

interface Location {
  id: string;
  name: string;
  address?: string;
}

interface ManageLocationsDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  locations: Location[];
}

export function ManageLocationsDialog({ open, onClose, onSuccess, locations }: ManageLocationsDialogProps) {
  const [editing, setEditing] = useState<string | null>(null);
  const [name, setName] = useState('');
  const [address, setAddress] = useState('');
  const [loading, setLoading] = useState(false);

  const handleEdit = (loc: Location) => {
    setEditing(loc.id);
    setName(loc.name);
    setAddress(loc.address || '');
  };

  const handleCancel = () => {
    setEditing(null);
    setName('');
    setAddress('');
  };

  const handleSave = async () => {
    if (!name.trim()) {
      toast.error('Name is required');
      return;
    }
    setLoading(true);
    try {
      if (editing) {
        if (typeof api.updateLocation === 'function') {
          await api.updateLocation(editing, { name, address });
        } else {
          // fallback: PATCH request
          await api.put(`/locations/${editing}`, { name, address });
        }
        toast.success('Location updated');
      } else {
        if (typeof api.createLocation === 'function') {
          await api.createLocation({ name, address });
        } else {
          // fallback: POST request
          await api.post('/locations', { name, address });
        }
        toast.success('Location created');
      }
      handleCancel();
      onSuccess();
    } catch (e) {
      toast.error('Failed to save location');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Delete this location?')) return;
    setLoading(true);
    try {
      if (typeof api.deleteLocation === 'function') {
        await api.deleteLocation(id);
      } else {
        // fallback: DELETE request
        await api.delete(`/locations/${id}`);
      }
      toast.success('Location deleted');
      onSuccess();
    } catch (e) {
      toast.error('Failed to delete location');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Manage Inventory Locations</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label>Name</Label>
            <Input value={name} onChange={e => setName(e.target.value)} placeholder="Location name" />
          </div>
          <div>
            <Label>Address</Label>
            <Input value={address} onChange={e => setAddress(e.target.value)} placeholder="Address (optional)" />
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleCancel} disabled={loading}>
              Cancel
            </Button>
            <Button type="button" onClick={handleSave} disabled={loading}>
              {editing ? 'Update' : 'Add'} Location
            </Button>
          </DialogFooter>
        </div>
        <div className="mt-6">
          <h4 className="font-semibold mb-2">Existing Locations</h4>
          <ul className="space-y-2">
            {locations.map((loc: Location) => (
              <li key={loc.id} className="flex items-center justify-between border rounded p-2">
                <div>
                  <div className="font-medium">{loc.name}</div>
                  {loc.address && <div className="text-xs text-muted-foreground">{loc.address}</div>}
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline" onClick={() => handleEdit(loc)} disabled={loading}>Edit</Button>
                  <Button size="sm" variant="destructive" onClick={() => handleDelete(loc.id)} disabled={loading}>Delete</Button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </DialogContent>
    </Dialog>
  );
} 