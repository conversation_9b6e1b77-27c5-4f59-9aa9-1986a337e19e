"use client";

import { useState } from "react";
import { DashboardSidebar } from "./dashboard-sidebar";
import { DashboardHeader } from "./dashboard-header";
import { cn } from "@/lib/utils";

interface DashboardLayoutProps {
    children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
    const [sidebarOpen, setSidebarOpen] = useState(false);

    return (
        <div className="min-h-screen bg-background">
            {/* Sidebar */}
            <DashboardSidebar open={sidebarOpen} onOpenChange={setSidebarOpen} />

            {/* Main content */}
            <div className={cn(
                "transition-all duration-300 ease-in-out",
                "lg:pl-64" // Always show sidebar on large screens
            )}>
                {/* Header */}
                <DashboardHeader onMenuClick={() => setSidebarOpen(true)} />

                {/* Page content */}
                <main className="p-6">
                    {children}
                </main>
            </div>

            {/* Mobile sidebar overlay */}
            {sidebarOpen && (
                <div
                    className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 lg:hidden"
                    onClick={() => setSidebarOpen(false)}
                />
            )}
        </div>
    );
}
