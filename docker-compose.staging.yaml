version: "3.8"

services:
  database:
    image: postgres:15-alpine
    container_name: peptides_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: peptides_db
      POSTGRES_USER: peptides_user
      POSTGRES_PASSWORD: peptides_password_2024
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5444:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U peptides_user -d peptides_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    image: harshitdkanodia/peptides_api:latest
    container_name: peptides_api
    restart: unless-stopped
    depends_on:
      database:
        condition: service_healthy
    environment:
      # Default environment variables for staging. Change these for production!
      NODE_ENV: staging
      DATABASE_URL: ***************************************************************/peptides_db
      JWT_SECRET: your_jwt_secret_key_here_change_in_production
      STRIPE_SECRET_KEY: your_stripe_secret_key_here
      STRIPE_WEBHOOK_SECRET: your_stripe_webhook_secret_here
      EMAIL_HOST: smtp.gmail.com
      EMAIL_PORT: 587
      EMAIL_USER: <EMAIL>
      EMAIL_PASSWORD: your_app_password_here
      CORS_ORIGIN: https://peptide.stmin.dev
      PORT: 3001
    ports:
      - "3666:3001"

  frontend:
    image: harshitdkanodia/peptides_nextjs:latest
    container_name: peptides_frontend
    restart: unless-stopped
    depends_on:
      - api
    environment:
      # Default environment variables for staging. Change these for production!
      NODE_ENV: staging
      NEXT_PUBLIC_API_URL: https://pepapi.stmin.dev/api
    ports:
      - "3667:3000"

  redis:
    image: redis:7-alpine
    container_name: peptides_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
