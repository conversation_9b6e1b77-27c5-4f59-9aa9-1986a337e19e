'use client';

import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Edit, MoreHorizontal, Trash2, MapPin, User, Building, Crown } from 'lucide-react';
import { Customer } from '@/lib/api';
import { saveAs } from 'file-saver';

interface CustomersTableProps {
  customers: Customer[];
  loading: boolean;
  onEdit: (customer: Customer) => void;
  onDelete: (customerId: string) => void;
  onManageAddresses: (customer: Customer) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const CustomerTypeBadge = ({ type }: { type: string }) => {
  const variants: { [key: string]: { variant: "default" | "secondary" | "destructive" | "outline", label: string, icon: any } } = {
    B2C: { variant: "outline", label: "B2C", icon: User },
    B2B: { variant: "secondary", label: "B2B", icon: Building },
    ENTERPRISE: { variant: "default", label: "Enterprise", icon: Crown }
  };

  const config = variants[type] || { variant: "outline", label: type, icon: User };
  const Icon = config.icon;
  
  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
};

const StatusBadge = ({ isActive }: { isActive: boolean }) => {
  return (
    <Badge variant={isActive ? "default" : "secondary"}>
      {isActive ? "Active" : "Inactive"}
    </Badge>
  );
};

function customersToCSV(customers: Customer[]): string {
  const headers = ['First Name', 'Last Name', 'Email', 'Type', 'Status'];
  const rows = customers.map(c => [
    c.firstName,
    c.lastName,
    c.email,
    c.customerType,
    c.isActive ? 'Active' : 'Inactive'
  ]);
  return [headers, ...rows].map(r => r.map(x => `"${x}"`).join(',')).join('\n');
}

export function CustomersTable({
  customers,
  loading,
  onEdit,
  onDelete,
  onManageAddresses,
  currentPage,
  totalPages,
  onPageChange
}: CustomersTableProps) {
  const [selected, setSelected] = useState<string[]>([]);
  const [bulkAction, setBulkAction] = useState<null | 'delete' | 'export'>(null);

  const allSelected = customers.length > 0 && selected.length === customers.length;
  const toggleSelectAll = () => {
    if (allSelected) setSelected([]);
    else setSelected(customers.map(c => c.id));
  };
  const toggleSelect = (id: string) => {
    setSelected(sel => sel.includes(id) ? sel.filter(x => x !== id) : [...sel, id]);
  };

  const handleBulkDelete = async () => {
    if (selected.length === 0) return;
    if (!window.confirm(`Deactivate ${selected.length} customers?`)) return;
    // Call backend bulk delete endpoint
    await fetch('/api/customers/bulk-delete', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ids: selected }),
    });
    setSelected([]);
    setBulkAction(null);
    // Optionally, refresh customers list here
  };

  const handleDelete = (customer: Customer) => {
    setSelectedCustomer(customer);
    onDelete(customer.id);
  };

  const handleBulkExport = () => {
    const selectedCustomers = customers.filter(c => selected.includes(c.id));
    const csv = customersToCSV(selectedCustomers);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, 'customers-export.csv');
    setBulkAction(null);
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-muted animate-pulse rounded" />
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead><input type="checkbox" checked={allSelected} onChange={toggleSelectAll} /></TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Contact</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Orders</TableHead>
            <TableHead>Created</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {customers.map((customer) => (
            <TableRow key={customer.id}>
              <TableCell><input type="checkbox" checked={selected.includes(customer.id)} onChange={() => toggleSelect(customer.id)} /></TableCell>
              <TableCell>
                <div className="flex items-center space-x-3">
                  <Avatar>
                    <AvatarImage src={`/avatars/${customer.id}.jpg`} />
                    <AvatarFallback>
                      {getInitials(customer.firstName, customer.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">
                      {customer.firstName} {customer.middleName} {customer.lastName}
                    </div>
                    <div className="text-sm text-muted-foreground">ID: {customer.id}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="space-y-1">
                  <div className="text-sm">{customer.email}</div>
                  <div className="text-sm text-muted-foreground">{customer.mobile}</div>
                </div>
              </TableCell>
              <TableCell>
                <CustomerTypeBadge type={customer.customerType} />
              </TableCell>
              <TableCell>
                <StatusBadge isActive={customer.isActive} />
              </TableCell>
              <TableCell>
                <div className="text-sm">
                  {customer._count?.orders || 0} orders
                </div>
              </TableCell>
              <TableCell>
                <div className="text-sm">
                  {new Date(customer.createdAt).toLocaleDateString()}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => onEdit(customer)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Customer
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onManageAddresses(customer)}>
                      <MapPin className="mr-2 h-4 w-4" />
                      Manage Addresses
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => handleDelete(customer)}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Deactivate Customer
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious 
                onClick={() => onPageChange(currentPage - 1)}
                className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
              />
            </PaginationItem>
            
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => onPageChange(page)}
                    isActive={currentPage === page}
                    className="cursor-pointer"
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              );
            })}
            
            {totalPages > 5 && (
              <>
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink
                    onClick={() => onPageChange(totalPages)}
                    isActive={currentPage === totalPages}
                    className="cursor-pointer"
                  >
                    {totalPages}
                  </PaginationLink>
                </PaginationItem>
              </>
            )}
            
            <PaginationItem>
              <PaginationNext 
                onClick={() => onPageChange(currentPage + 1)}
                className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
      {selected.length > 0 && (
        <div className="bulk-actions-toolbar">
          <Button variant="outline" onClick={handleBulkExport}>Export Selected ({selected.length})</Button>
          <button onClick={() => setBulkAction('delete')}>Deactivate Selected</button>
        </div>
      )}
      {bulkAction === 'delete' && (
        <div className="bulk-actions-toolbar">
          <button onClick={handleBulkDelete}>Confirm Bulk Deletion</button>
          <button onClick={() => setBulkAction(null)}>Cancel</button>
        </div>
      )}
    </div>
  );
} 